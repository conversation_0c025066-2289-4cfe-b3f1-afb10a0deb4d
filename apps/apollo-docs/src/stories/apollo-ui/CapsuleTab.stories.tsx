import { useState } from "react"
import { CapsuleTab } from "@apollo/ui"
import type { Meta, StoryObj } from "@storybook/react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"

// Example data
const tabs = [
  { id: "alpha", label: "Alpha" },
  { id: "beta", label: "Beta" },
  { id: "gamma", label: "Gamma" },
]

const meta: Meta<typeof CapsuleTab> = {
  title: "@apollo∕ui/Components/Layout/CapsuleTab",
  component: CapsuleTab,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "The `CapsuleTab` component is used to create a tabbed interface with a capsule-style design. It allows users to switch between different views or content.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { CapsuleTab } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="capsuletab-props">Props</h2>
          <ArgTypes />
          <h2 id="capsuletab-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The CapsuleTab component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloCapsuleTab-root",
                description: "Styles applied to the capsule tab root container",
                usageNotes: "Use for overall tab styling and positioning",
              },
              {
                cssClassName: ".ApolloCapsuleTab-container",
                description: "Styles applied to the tab container",
                usageNotes: "Contains flex layout and border styling for the tab group",
              },
              {
                cssClassName: ".ApolloCapsuleTab-item",
                description: "Styles applied to individual tab items",
                usageNotes: "Contains tab button styling including padding and typography",
              },
              {
                cssClassName: ".ApolloCapsuleTab-itemSelected",
                description: "Styles applied to the selected tab item",
                usageNotes: "Applies active styling with background color and text color changes",
              },
              {
                cssClassName: ".ApolloCapsuleTab-itemText",
                description: "Styles applied to the text inside tab items",
                usageNotes: "Contains font size and weight styling for tab text",
              },
            ]}
          />
          <h2 id="capsuletab-examples">Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  argTypes: {
    tabs: {
      description: "Array of tab objects with `id` and `label`.",
      control: "object",
    },
    selectedIndex: {
      description: "Index of the currently selected tab.",
      control: "number",
    },
    onSelect: {
      description: "Callback when a tab is selected.",
      action: "selected",
    },
    className: {
      description: "Additional class names for the root element.",
      control: "text",
    },
  },
}
export default meta

type Story = StoryObj<typeof CapsuleTab>

export const Basic: Story = {
  render: (args) => {
    const [selectedIndex, setSelectedIndex] = useState(0)

    return (
      <CapsuleTab
        {...args}
        tabs={args.tabs ?? tabs}
        selectedIndex={selectedIndex}
        onSelect={setSelectedIndex}
      />
    )
  },
  args: {
    tabs,
    selectedIndex: 0,
  },
}