import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { SortingIcon, Typography, IconButton } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * SortingIcon component
 *
 * The SortingIcon component indicates the sorting status of a table column, providing users 
 * with a visual representation of the sorting state. It displays up and down arrows that 
 * change appearance based on the current sorting direction.
 *
 * Notes:
 * - Default status is "default" (both arrows inactive);
 * - Available statuses: "default" | "asc" | "desc";
 * - Commonly used in table headers for sortable columns;
 * - Provides clear visual feedback for sorting state.
 */
const meta = {
  title: "@apollo∕ui/Components/Data Display/SortingIcon",
  component: SortingIcon,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2789-23146&m=dev",
    },
    docs: {
      description: {
        component:
          "The SortingIcon component renders a visual indicator for table column sorting status. It displays up and down caret icons that highlight based on the current sorting direction (ascending, descending, or default/unsorted).",
      },
      page: () => (
        <>
          <Title />
          <Subtitle/>
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { SortingIcon } from "@apollo/ui"`} language="tsx" />
          <h2 id="sortingicon-props">Props</h2>
          <ArgTypes />
          <h2 id="sortingicon-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use SortingIcon in table headers to indicate sortable columns.",
              "Always provide clear visual feedback when the sorting state changes.",
              "Consider using SortingIcon with clickable table headers for better user experience.",
              "Ensure the sorting icon is positioned consistently across all sortable columns.",
            ]}
          />
          <h2 id="sortingicon-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                The component includes built-in <code>aria-label</code> attributes 
                for the ascending and descending icons to support screen readers.
              </>,
              <>
                When used in table headers, ensure the parent element (like a button) 
                has appropriate <code>aria-label</code> or <code>aria-describedby</code> 
                attributes to describe the sorting action.
              </>,
            ]}
          />
          <h2 id="sortingicon-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The SortingIcon component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloSortingIcon-root",
                description: "Styles applied to the root container element",
                usageNotes: "Use for overall sorting icon styling and positioning",
              },
              {
                cssClassName: ".ApolloSortingIcon-iconASC",
                description: "Styles applied to the ascending (up) arrow icon",
                usageNotes: "Use for customizing the ascending arrow appearance",
              },
              {
                cssClassName: ".ApolloSortingIcon-iconDESC",
                description: "Styles applied to the descending (down) arrow icon",
                usageNotes: "Use for customizing the descending arrow appearance",
              },
            ]}
          />
          <h2 id="sortingicon-examples">Examples</h2>
          <Stories title="" />
          <h2 id="sortingicon-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                        <span>Product Name</span>
                        <SortingIcon status="asc" />
                      </div>
                      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                        <span>Price</span>
                        <SortingIcon status="default" />
                      </div>
                    </div>
                  ),
                  description: "Use SortingIcon consistently in table headers with clear labels",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <SortingIcon status="asc" />
                      <span>Product Name</span>
                      <SortingIcon status="default" />
                      <span>Price</span>
                    </div>
                  ),
                  description:
                    "Don't place sorting icons inconsistently or without clear association to column labels",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <IconButton aria-label="Sort by product name">
                        <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                          <span>Product Name</span>
                          <SortingIcon status="asc" />
                        </div>
                      </IconButton>
                    </div>
                  ),
                  description: "Make sorting interactive with proper accessibility labels",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                        <span>Product Name</span>
                        <SortingIcon status="asc" />
                      </div>
                    </div>
                  ),
                  description:
                    "Don't use sorting icons without making them interactive or providing accessibility context",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    status: {
      control: { type: "radio" },
      options: ["default", "asc", "desc"],
      description: "The sorting status of the table column. Default is 'default'.",
      table: {
        type: { summary: '"default" | "asc" | "desc"' },
        defaultValue: { summary: "default" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the component.",
      table: { type: { summary: "string" } },
    },
  },
  args: {
    status: "default",
  },
} satisfies Meta<typeof SortingIcon>

export default meta

type Story = StoryObj<typeof SortingIcon>

/** Default SortingIcon (demonstrates default unsorted state) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview SortingIcon with default settings. The component defaults to status 'default', showing both arrows in an inactive state.",
      },
    },
  },
  args: {
    status: "default",
  },
}

/** SortingIcon with different sorting states (default, ascending, descending) */
export const SortingStates: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available SortingIcon states: default (unsorted), ascending (asc), and descending (desc).",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 32, alignItems: "center" }}>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 8 }}>
        <Typography level="bodySmall">Default</Typography>
        <SortingIcon {...args} status="default" />
      </div>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 8 }}>
        <Typography level="bodySmall">Ascending</Typography>
        <SortingIcon {...args} status="asc" />
      </div>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 8 }}>
        <Typography level="bodySmall">Descending</Typography>
        <SortingIcon {...args} status="desc" />
      </div>
    </div>
  ),
}

/** Interactive SortingIcon with state management */
export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Interactive example showing how SortingIcon can be used with state management to cycle through sorting states.",
      },
    },
  },
  render: () => {
    function InteractiveDemo() {
      const [sortStatus, setSortStatus] = useState<"default" | "asc" | "desc">("default")

      const handleSort = () => {
        if (sortStatus === "default") setSortStatus("asc")
        else if (sortStatus === "asc") setSortStatus("desc")
        else setSortStatus("default")
      }

      const getStatusText = () => {
        switch (sortStatus) {
          case "asc": return "Ascending"
          case "desc": return "Descending"
          default: return "Unsorted"
        }
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 16, alignItems: "center" }}>
          <button
            onClick={handleSort}
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              padding: "8px 16px",
              backgroundColor: "#ffffff",
              border: "1px solid #dee2e6",
              borderRadius: 6,
              cursor: "pointer",
              fontWeight: 500,
            }}
          >
            <span>Product Name</span>
            <SortingIcon status={sortStatus} />
          </button>
          <Typography level="bodySmall" style={{ color: "#6c757d" }}>
            Current status: {getStatusText()} - Click to cycle through states
          </Typography>
        </div>
      )
    }
    return <InteractiveDemo />
  },
}

/** SortingIcon with custom styling */
export const CustomStyling: Story = {
  parameters: {
    docs: {
      description: {
        story: "SortingIcon with custom CSS classes and styling applied.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", gap: 32, alignItems: "center" }}>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 8 }}>
        <Typography level="bodySmall">Default Styling</Typography>
        <SortingIcon status="asc" />
      </div>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 8 }}>
        <Typography level="bodySmall">Custom Class</Typography>
        <SortingIcon
          status="desc"
          className="custom-sorting-icon"
          style={{ transform: "scale(1.2)" }}
        />
      </div>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 8 }}>
        <Typography level="bodySmall">Inline Styling</Typography>
        <SortingIcon
          status="asc"
          style={{
            opacity: 0.7,
            filter: "hue-rotate(45deg)"
          }}
        />
      </div>
    </div>
  ),
}

/** Real-world table example with SortingIcon */
export const TableExample: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive example showing SortingIcon used in a realistic data table with interactive sorting functionality.",
      },
    },
  },
  render: () => {
    function TableDemo() {
      const [sortConfig, setSortConfig] = useState<{
        key: string
        direction: "default" | "asc" | "desc"
      }>({ key: "", direction: "default" })

      const data = [
        { id: 1, name: "Product A", price: 29.99, stock: 150, category: "Electronics" },
        { id: 2, name: "Product B", price: 19.99, stock: 75, category: "Books" },
        { id: 3, name: "Product C", price: 39.99, stock: 200, category: "Electronics" },
        { id: 4, name: "Product D", price: 9.99, stock: 50, category: "Books" },
      ]

      const handleSort = (key: string) => {
        let direction: "default" | "asc" | "desc" = "asc"

        if (sortConfig.key === key) {
          if (sortConfig.direction === "asc") direction = "desc"
          else if (sortConfig.direction === "desc") direction = "default"
        }

        setSortConfig({ key, direction })
      }

      const getSortStatus = (key: string) => {
        return sortConfig.key === key ? sortConfig.direction : "default"
      }

      return (
        <div style={{ width: "100%", maxWidth: "800px" }}>
          <table style={{ width: "100%", borderCollapse: "collapse", backgroundColor: "#ffffff" }}>
            <thead>
              <tr style={{ backgroundColor: "#f8f9fa" }}>
                {[
                  { key: "name", label: "Product Name" },
                  { key: "price", label: "Price" },
                  { key: "stock", label: "Stock" },
                  { key: "category", label: "Category" },
                ].map((column) => (
                  <th
                    key={column.key}
                    style={{
                      padding: "12px 16px",
                      textAlign: "left",
                      borderBottom: "2px solid #dee2e6",
                      cursor: "pointer",
                      userSelect: "none",
                    }}
                    onClick={() => handleSort(column.key)}
                  >
                    <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                      <span style={{ fontWeight: 600 }}>{column.label}</span>
                      <SortingIcon status={getSortStatus(column.key)} />
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.map((item) => (
                <tr key={item.id} style={{ borderBottom: "1px solid #e9ecef" }}>
                  <td style={{ padding: "12px 16px" }}>{item.name}</td>
                  <td style={{ padding: "12px 16px" }}>${item.price}</td>
                  <td style={{ padding: "12px 16px" }}>{item.stock}</td>
                  <td style={{ padding: "12px 16px" }}>{item.category}</td>
                </tr>
              ))}
            </tbody>
          </table>
          <div style={{ marginTop: 16, fontSize: 14, color: "#6c757d", fontStyle: "italic" }}>
            Click on column headers to sort. Current sort: {sortConfig.key || "none"} ({sortConfig.direction})
          </div>
        </div>
      )
    }
    return <TableDemo />
  },
}