import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { <PERSON><PERSON>, <PERSON>ton, Typography } from "@apollo/ui"
import {
  CheckCircle,
  ExclamationCircle,
  Download,
  Setting,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Alert component
 *
 * The Alert component displays brief, important messages to users in a prominent way.
 * It supports different color variants for various message types, custom icons, titles,
 * descriptions, and dismissible functionality.
 *
 * Notes:
 * - Default color is "info";
 * - Available colors: "success" | "info" | "warning" | "error";
 * - Supports custom start and end decorators;
 * - Can be made dismissible with onClose prop;
 * - Supports fullWidth layout option.
 */
const meta = {
  title: "@apollo∕ui/Components/Feedback/Alert",
  component: Alert,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2637-6140&m=dev",
    },
    docs: {
      description: {
        component:
          "The Alert component displays brief, important messages to users. It supports different color variants for various message types, custom icons, titles, descriptions, and dismissible functionality.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Alert } from "@apollo/ui"`} language="tsx" />
          <h2 id="alert-props">Props</h2>
          <ArgTypes />
          <h2 id="alert-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use appropriate color variants to convey the right message type - success for confirmations, warning for cautions, error for problems, info for general information",
              "Keep alert messages concise and actionable",
              "Use titles for the main message and descriptions for additional context",
              "Make alerts dismissible when they don't require persistent attention",
              "Use fullWidth for alerts that need to span the entire container",
              "Consider using custom decorators to enhance the message with relevant icons or actions",
            ]}
          />
          <h2 id="alert-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                The Alert component automatically includes <code>role="alert"</code> for screen readers.
              </>,
              <>
                Use clear, descriptive titles and descriptions that convey the message without relying solely on color.
              </>,
              <>
                When using custom decorators, ensure they provide meaningful context and don't rely only on visual cues.
              </>,
              <>
                For dismissible alerts, ensure the close button is keyboard accessible and has appropriate focus management.
              </>,
              <>
                Consider the reading order when placing alerts in your layout - important alerts should appear early in the document flow.
              </>,
            ]}
          />
          <h2 id="alert-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Alert component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts of the component."
            data={[
              {
                cssClassName: ".ApolloAlert-root",
                description: "Styles applied to the root alert element",
                usageNotes: "Use for overall alert styling, positioning, and base properties",
              },
              {
                cssClassName: ".ApolloAlert-title",
                description: "Styles applied to the title element",
                usageNotes: "Use for customizing title typography and spacing",
              },
              {
                cssClassName: ".ApolloAlert-description",
                description: "Styles applied to the description element",
                usageNotes: "Use for customizing description text styling",
              },
              {
                cssClassName: ".ApolloAlert-startDecorator",
                description: "Styles applied to the start decorator container",
                usageNotes: "Use for positioning and styling start icons or decorators",
              },
              {
                cssClassName: ".ApolloAlert-endDecorator",
                description: "Styles applied to the end decorator container",
                usageNotes: "Use for positioning and styling end decorators",
              },
              {
                cssClassName: ".ApolloAlert-closeButton",
                description: "Styles applied to the close button",
                usageNotes: "Use for customizing the close button appearance and behavior",
              },
              {
                cssClassName: ".ApolloAlert-action",
                description: "Styles applied to the action container",
                usageNotes: "Use for styling action buttons or elements within the alert",
              },
            ]}
          />
          <h2 id="alert-examples">Examples</h2>
          <Stories title="" />
          <h2 id="alert-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 12, width: 300 }}>
                      <Alert
                        color="success"
                        title="File uploaded successfully"
                        description="Your document has been saved to the cloud."
                      />
                      <Alert
                        color="error"
                        title="Upload failed"
                        description="Please check your connection and try again."
                      />
                    </div>
                  ),
                  description: "Use clear, specific messages that tell users what happened and what to do next",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 12, width: 300 }}>
                      <Alert
                        color="success"
                        title="Success"
                        description="Something good happened."
                      />
                      <Alert
                        color="error"
                        title="Error"
                        description="Something went wrong."
                      />
                    </div>
                  ),
                  description: "Avoid vague messages that don't provide actionable information",
                },
              },
              {
                positive: {
                  component: (
                    <Alert
                      color="warning"
                      title="Storage almost full"
                      description="You're using 95% of your storage space."
                      action={
                        <Button size="small" variant="outline">
                          Upgrade Plan
                        </Button>
                      }
                    />
                  ),
                  description: "Include relevant actions when users can take immediate steps",
                },
                negative: {
                  component: (
                    <Alert
                      color="info"
                      title="Information"
                      description="Here's some information for you to know about."
                    />
                  ),
                  description: "Avoid alerts for non-actionable information that could be displayed elsewhere",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    color: {
      control: { type: "radio" },
      options: ["success", "info", "warning", "error"],
      description: "Color variant of the alert. Default is 'info'.",
      table: {
        type: { summary: '"success" | "info" | "warning" | "error"' },
        defaultValue: { summary: "info" },
      },
    },
    title: {
      control: { type: "text" },
      description: "The title content of the alert.",
      table: { type: { summary: "ReactNode" } },
    },
    description: {
      control: { type: "text" },
      description: "The description content of the alert.",
      table: { type: { summary: "ReactNode" } },
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the alert takes the full width of its container.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    startDecorator: {
      control: false,
      description: "Element to display at the start of the alert (replaces default icon).",
      table: { type: { summary: "ReactNode" } },
    },
    endDecorator: {
      control: false,
      description: "Element to display at the end of the alert.",
      table: { type: { summary: "ReactNode" } },
    },
    action: {
      control: false,
      description: "Action element to display within the alert content.",
      table: { type: { summary: "ReactNode" } },
    },
    onClose: {
      control: false,
      description: "Callback fired when the close button is clicked. Shows close button when provided.",
      table: {
        type: {
          summary: "ReactEventHandler",
        },
      },
    },
  },
  args: {
    color: "info",
    title: "Alert Title",
    description: "This is an alert description that provides additional context.",
    fullWidth: false,
  },
} satisfies Meta<typeof Alert>

export default meta

type Story = StoryObj<typeof Alert>

/** Default Alert (demonstrates default info variant) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Alert with default settings. The component defaults to color 'info' and includes an appropriate icon.",
      },
    },
  },
  args: {
    title: "Information",
    description: "This is a basic informational alert.",
  },
}

/** Alert with different color variants */
export const Colors: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available color variants: success, info, warning, and error. Each variant has its own semantic meaning and appropriate icon.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16, width: 400 }}>
      <Alert
        {...args}
        color="success"
        title="Success"
        description="Your action was completed successfully."
      />
      <Alert
        {...args}
        color="info"
        title="Information"
        description="Here's some helpful information for you."
      />
      <Alert
        {...args}
        color="warning"
        title="Warning"
        description="Please be careful with this action."
      />
      <Alert
        {...args}
        color="error"
        title="Error"
        description="Something went wrong. Please try again."
      />
    </div>
  ),
}

/** Alert with title only (no description) */
export const TitleOnly: Story = {
  parameters: {
    docs: {
      description: {
        story: "Simple alerts with only titles, no descriptions. Useful for brief notifications.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", flexDirection: "column", gap: 12, width: 300 }}>
      <Alert {...args} color="success" title="File saved successfully" description={undefined} />
      <Alert {...args} color="info" title="Update available" description={undefined} />
      <Alert {...args} color="warning" title="Storage almost full" description={undefined} />
      <Alert {...args} color="error" title="Connection failed" description={undefined} />
    </div>
  ),
}

/** Alert with custom decorators */
export const CustomDecorators: Story = {
  parameters: {
    docs: {
      description: {
        story: "Alerts with custom start and end decorators including custom icons, progress indicators, and action buttons.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16, width: 450 }}>
      <Alert
        {...args}
        color="info"
        title="Download Progress"
        description="Installing updates... 65% complete"
        startDecorator={<Download size={20} />}
        endDecorator={
          <div
            style={{
              width: "60px",
              height: "6px",
              background: "#e0e0e0",
              borderRadius: "3px",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                width: "65%",
                height: "100%",
                background: "#007bff",
                transition: "width 0.3s ease",
              }}
            />
          </div>
        }
      />
      <Alert
        {...args}
        color="warning"
        title="Security Alert"
        description="Unusual login detected from new device"
        startDecorator={<ExclamationCircle size={20} />}
        endDecorator={
          <div style={{ display: "flex", gap: "4px" }}>
            <Button size="small" variant="text" color="primary">
              Approve
            </Button>
            <Button size="small" variant="text" color="negative">
              Block
            </Button>
          </div>
        }
      />
      <Alert
        {...args}
        color="success"
        title="Backup Complete"
        description="All files backed up to cloud storage"
        startDecorator={<CheckCircle size={20} />}
        endDecorator={
          <div style={{ fontSize: "12px", color: "#666", fontFamily: "monospace" }}>
            2:34 PM
          </div>
        }
      />
    </div>
  ),
}

/** Dismissible alerts with close functionality */
export const Dismissible: Story = {
  parameters: {
    docs: {
      description: {
        story: "Dismissible alerts with close button functionality. Users can dismiss alerts they no longer need to see.",
      },
    },
  },
  render: () => {
    function DismissibleDemo() {
      const [alerts, setAlerts] = useState([
        {
          id: 1,
          color: "success" as const,
          title: "Upload Complete",
          description: "Your files have been uploaded successfully.",
        },
        {
          id: 2,
          color: "warning" as const,
          title: "Session Expiring",
          description: "Your session will expire in 5 minutes.",
        },
        {
          id: 3,
          color: "info" as const,
          title: "New Feature",
          description: "Check out our new dashboard design!",
        },
      ])

      const handleClose = (id: number) => {
        setAlerts((prev) => prev.filter((alert) => alert.id !== id))
      }

      const resetAlerts = () => {
        setAlerts([
          {
            id: Date.now() + 1,
            color: "success",
            title: "Upload Complete",
            description: "Your files have been uploaded successfully.",
          },
          {
            id: Date.now() + 2,
            color: "warning",
            title: "Session Expiring",
            description: "Your session will expire in 5 minutes.",
          },
          {
            id: Date.now() + 3,
            color: "info",
            title: "New Feature",
            description: "Check out our new dashboard design!",
          },
        ])
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 12, width: 400 }}>
          {alerts.map((alert) => (
            <Alert
              key={alert.id}
              color={alert.color}
              title={alert.title}
              description={alert.description}
              onClose={() => handleClose(alert.id)}
            />
          ))}

          {alerts.length === 0 && (
            <div
              style={{
                padding: "20px",
                textAlign: "center",
                color: "#666",
                border: "2px dashed #ddd",
                borderRadius: "8px",
              }}
            >
              All alerts dismissed!
              <br />
              <Button
                onClick={resetAlerts}
                size="small"
                variant="outline"
                style={{ marginTop: "8px" }}
              >
                Reset Alerts
              </Button>
            </div>
          )}
        </div>
      )
    }
    return <DismissibleDemo />
  },
}

/** Alert with full width layout */
export const FullWidth: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "Full width alerts that expand to their container width. Useful for page-level notifications.",
      },
    },
  },
  render: (args) => (
    <div style={{ width: "100%", display: "flex", flexDirection: "column", gap: 12 }}>
      <Alert
        {...args}
        fullWidth
        color="info"
        title="System Maintenance"
        description="We'll be performing scheduled maintenance tonight from 11 PM to 1 AM EST. During this time, some features may be unavailable."
      />
      <Alert
        {...args}
        fullWidth
        color="warning"
        title="Important Update"
        description="Please review the new terms of service before your next login."
        onClose={() => {}}
      />
      <Alert
        {...args}
        fullWidth
        color="success"
        title="Account Verified"
        description="Your email address has been successfully verified. You now have access to all features."
      />
    </div>
  ),
}

/** Alert with action buttons */
export const WithActions: Story = {
  parameters: {
    docs: {
      description: {
        story: "Alerts with action buttons that allow users to take immediate action on the alert message.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16, width: 450 }}>
      <Alert
        {...args}
        color="warning"
        title="Storage Limit Reached"
        description="You're using 95% of your storage space."
        action={
          <div style={{ display: "flex", gap: 8 }}>
            <Button size="small" variant="filled">
              Upgrade Plan
            </Button>
            <Button size="small" variant="outline">
              Manage Files
            </Button>
          </div>
        }
      />
      <Alert
        {...args}
        color="info"
        title="New Feature Available"
        description="Try our new dark mode theme."
        action={
          <Button size="small" variant="outline" startDecorator={<Setting size={16} />}>
            Open Setting
          </Button>
        }
        onClose={() => {}}
      />
      <Alert
        {...args}
        color="error"
        title="Connection Lost"
        description="Unable to connect to the server."
        action={
          <Button size="small" variant="filled" color="negative">
            Retry Connection
          </Button>
        }
      />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "A comprehensive showcase of alert states and variations including different colors, with and without descriptions, and various decorator combinations.",
      },
    },
  },
  render: () => (
    <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: 20 }}>
      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography level="bodyLarge">Basic Alerts</Typography>
        <Alert color="success" title="Success Alert" description="Operation completed successfully." />
        <Alert color="info" title="Info Alert" description="Here's some information for you." />
        <Alert color="warning" title="Warning Alert" description="Please be careful with this action." />
        <Alert color="error" title="Error Alert" description="Something went wrong." />
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography level="bodyLarge">Title Only</Typography>
        <Alert color="success" title="File saved successfully" />
        <Alert color="info" title="Update available" />
        <Alert color="warning" title="Storage almost full" />
        <Alert color="error" title="Connection failed" />
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography level="bodyLarge">With Close Button</Typography>
        <Alert color="success" title="Upload Complete" description="Files uploaded successfully." onClose={() => {}} />
        <Alert color="info" title="New Feature" description="Check out our latest update." onClose={() => {}} />
        <Alert color="warning" title="Session Expiring" description="Your session will expire soon." onClose={() => {}} />
        <Alert color="error" title="Upload Failed" description="Please try again." onClose={() => {}} />
      </div>
    </div>
  ),
}