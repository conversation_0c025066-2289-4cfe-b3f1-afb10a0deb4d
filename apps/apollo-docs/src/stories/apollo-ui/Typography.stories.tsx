import React from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

/**
 * Typography component
 *
 * The Typography component provides styled, accessible text with multiple variants,
 * semantic levels, colors, and alignment options for consistent typography across
 * the Apollo design system.
 *
 * Notes:
 * - Default level is "body1";
 * - Default alignment is "left";
 * - Supports both legacy and M3 typography scales;
 * - Available levels include displays, headlines, titles, body text, and labels;
 * - Available colors: "default" | "negative" | "warning" | "success" | "process" | "primary" | "secondary" | "tertiary".
 */
const meta = {
  title: "@apollo∕ui/Components/Data Display/Typography",
  component: Typography,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=11113-11712&m=dev",
    },
    docs: {
      description: {
        component:
          "The Typography component renders text with Apollo design system styling. Supports multiple semantic levels, colors, alignments, and both legacy and M3 typography scales.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Typography } from "@apollo/ui"`} language="tsx" />
          <h2 id="typography-props">Props</h2>
          <ArgTypes />
          <h2 id="typography-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Choose appropriate typography levels based on content importance",
              "Use display levels for large, prominent text like page titles",
              "Use body levels for regular content and reading text",
              "Use label levels for form labels and small UI text",
              "Apply colors consistently with your design system's semantic meaning",
              "Use gutterBottom for proper spacing between text elements",
              "Consider noWrap for single-line text that should truncate",
            ]}
          />
          <h2 id="typography-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Ensure sufficient color contrast between text and background
                in all color variants.
              </>,
              <>
                Use <code>level</code> prop to match visual appearance with
                semantic meaning when possible.
              </>,
              <>
                Avoid using color alone to convey information - combine with
                other visual cues when needed.
              </>,
              <>
                Use appropriate text sizes for readability - avoid very small
                text for important content.
              </>,
              <>
                Consider line length and spacing for optimal reading experience,
                especially for body text.
              </>,
            ]}
          />
          <h2 id="typography-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Typography component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different typography levels and states."
            data={[
              {
                cssClassName: ".ApolloTypography-root",
                description: "Styles applied to the root typography element",
                usageNotes: "Use for overall typography styling, positioning, and base properties",
              },
            ]}
          />
          <h2 id="typography-examples">Examples</h2>
          <Stories title="" />
          <h2 id="typography-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                      <Typography level="h1">Main Page Title</Typography>
                      <Typography level="h2">Section Heading</Typography>
                      <Typography level="bodyLarge">Important content paragraph</Typography>
                    </div>
                  ),
                  description: "Use proper heading hierarchy and appropriate levels for content importance",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                      <Typography level="h3">Main Page Title</Typography>
                      <Typography level="h1">Section Heading</Typography>
                      <Typography level="displayLarge">Regular paragraph text</Typography>
                    </div>
                  ),
                  description:
                    "Don't use heading levels out of order or display levels for regular content",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                      <Typography level="bodyLarge" color="default">Regular content</Typography>
                      <Typography level="bodyLarge" color="success">Success message</Typography>
                      <Typography level="bodyLarge" color="negative">Error message</Typography>
                    </div>
                  ),
                  description:
                    "Use colors that match the semantic meaning of your content",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                      <Typography level="bodyLarge" color="negative">Regular content</Typography>
                      <Typography level="bodyLarge" color="success">Error message</Typography>
                      <Typography level="bodyLarge" color="warning">Success message</Typography>
                    </div>
                  ),
                  description:
                    "Don't use colors that conflict with the semantic meaning of your content",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
                      <Typography level="h2" gutterBottom>Section Title</Typography>
                      <Typography level="bodyLarge">
                        This paragraph has proper spacing from the heading above.
                      </Typography>
                    </div>
                  ),
                  description:
                    "Use gutterBottom for proper spacing between text elements",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column" }}>
                      <Typography level="h2">Section Title</Typography>
                      <Typography level="bodyLarge">
                        This paragraph is too close to the heading above.
                      </Typography>
                    </div>
                  ),
                  description:
                    "Don't forget spacing between text elements - it affects readability",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    level: {
      control: { type: "select" },
      options: [
        // Legacy typography
        "display1", "display2", "h1", "h2", "h3", "h4", "h5", "body1", "body2", "caption", "textlink",
        // M3 typography
        "displayLarge", "displayMedium", "displaySmall",
        "headlineLarge", "headlineMedium", "headlineSmall",
        "titleLarge", "titleMedium", "titleSmall",
        "bodyLarge", "bodyLargeEmphasized", "bodyMedium", "bodyMediumEmphasized", "bodySmall", "bodySmallEmphasized",
        "labelLarge", "labelLargeEmphasized", "labelMedium", "labelMediumEmphasized", "labelSmall", "labelSmallEmphasized"
      ],
      description: "Typography level/variant that determines the visual style and semantic meaning. Default is 'bodyLarge'.",
      table: {
        type: { summary: "displayLarge | displayMedium | displaySmall | headlineLarge | headlineMedium | headlineSmall | titleLarge | titleMedium | titleSmall | bodyLarge | bodyLargeEmphasized | bodyMedium | bodyMediumEmphasized | bodySmall | bodySmallEmphasized | labelLarge | labelLargeEmphasized | labelMedium | labelMediumEmphasized | labelSmall | labelSmallEmphasized" },
        defaultValue: { summary: "bodyLarge" },
      },
    },
    align: {
      control: { type: "radio" },
      options: ["left", "center", "right", "justify"],
      description: "Text alignment. Default is 'left'.",
      table: {
        type: { summary: '"left" | "center" | "right" | "justify"' },
        defaultValue: { summary: "left" },
      },
    },
    color: {
      control: { type: "select" },
      options: ["default", "negative", "warning", "success", "process", "primary", "secondary", "tertiary"],
      description: "Color theme of the typography.",
      table: {
        type: { summary: '"default" | "negative" | "warning" | "success" | "process" | "primary"' },
        defaultValue: { summary: "default" },
      },
    },
    gutterBottom: {
      control: { type: "boolean" },
      description: "Adds bottom margin for spacing between text elements.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    noWrap: {
      control: { type: "boolean" },
      description: "Prevents text wrapping and adds ellipsis for overflow.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the typography element.",
      table: { type: { summary: "string" } },
    },
    children: {
      control: { type: "text" },
      description: "The text content to display.",
      table: { type: { summary: "ReactNode" } },
    },
  },
  args: {
    children: "Typography",
    level: "bodyLarge",
    align: "left",
    gutterBottom: false,
    noWrap: false,
  },
} satisfies Meta<typeof Typography>

export default meta

type Story = StoryObj<typeof Typography>

/** Default Typography (demonstrates default body1 level) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Typography with default settings. The component defaults to level 'body1' and alignment 'left'.",
      },
    },
  },
  args: {
    children: "Hi, I'm Typography.",
  },
}

/** Typography with M3 display levels */
export const DisplayLevels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases Material Design 3 display typography levels for large, prominent text.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
      <Typography level="displayLarge">Display Large - Hero text</Typography>
      <Typography level="displayMedium">Display Medium - Large titles</Typography>
      <Typography level="displaySmall">Display Small - Prominent headings</Typography>
    </div>
  ),
}

/** Typography with M3 headline levels */
export const HeadlineLevels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases Material Design 3 headline typography levels for page and section titles.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
      <Typography level="headlineLarge">Headline Large - Page titles</Typography>
      <Typography level="headlineMedium">Headline Medium - Section titles</Typography>
      <Typography level="headlineSmall">Headline Small - Subsection titles</Typography>
    </div>
  ),
}

/** Typography with M3 title levels */
export const TitleLevels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases Material Design 3 title typography levels for component and card titles.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
      <Typography level="titleLarge">Title Large - Component titles</Typography>
      <Typography level="titleMedium">Title Medium - Card titles</Typography>
      <Typography level="titleSmall">Title Small - List item titles</Typography>
    </div>
  ),
}

/** Typography with M3 body levels */
export const BodyLevels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases Material Design 3 body typography levels for content text, including emphasized variants.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
      <Typography level="bodyLarge">Body Large - Important content text</Typography>
      <Typography level="bodyLargeEmphasized">Body Large Emphasized - Important highlighted content</Typography>
      <Typography level="bodyMedium">Body Medium - Regular content text</Typography>
      <Typography level="bodyMediumEmphasized">Body Medium Emphasized - Regular highlighted content</Typography>
      <Typography level="bodySmall">Body Small - Secondary content text</Typography>
      <Typography level="bodySmallEmphasized">Body Small Emphasized - Secondary highlighted content</Typography>
    </div>
  ),
}

/** Typography with M3 label levels */
export const LabelLevels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases Material Design 3 label typography levels for UI labels and small text, including emphasized variants.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
      <Typography level="labelLarge">Label Large - Form labels</Typography>
      <Typography level="labelLargeEmphasized">Label Large Emphasized - Important form labels</Typography>
      <Typography level="labelMedium">Label Medium - Button labels</Typography>
      <Typography level="labelMediumEmphasized">Label Medium Emphasized - Important button labels</Typography>
      <Typography level="labelSmall">Label Small - Helper text</Typography>
      <Typography level="labelSmallEmphasized">Label Small Emphasized - Important helper text</Typography>
    </div>
  ),
}

/** Typography with different colors */
export const Colors: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available color variants for typography.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
      <Typography level="bodyLarge" color="default">Default color text</Typography>
      <Typography level="bodyLarge" color="primary">Primary color text</Typography>
      <Typography level="bodyLarge" color="negative">Negative/error color text</Typography>
      <Typography level="bodyLarge" color="warning">Warning color text</Typography>
      <Typography level="bodyLarge" color="success">Success color text</Typography>
      <Typography level="bodyLarge" color="process">Process color text</Typography>
    </div>
  ),
}

/** Typography with different alignments */
export const Alignments: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "Showcases all available text alignment options.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16, width: "100%" }}>
      <Typography level="bodyLarge" align="left">Left aligned text (default)</Typography>
      <Typography level="bodyLarge" align="center">Center aligned text</Typography>
      <Typography level="bodyLarge" align="right">Right aligned text</Typography>
      <Typography level="bodyLarge" align="justify">
        Justified text that will distribute words evenly across the line when the text is long enough to wrap to multiple lines, creating clean edges on both sides.
      </Typography>
    </div>
  ),
}

/** Typography with spacing options */
export const Spacing: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates the gutterBottom prop for adding spacing between typography elements.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div style={{ marginBottom: 32 }}>
        <Typography level="h3">Without gutterBottom</Typography>
        <Typography level="h4">Subheading</Typography>
        <Typography level="bodyLarge">
          This text is too close to the headings above, making it harder to read and understand the content hierarchy.
        </Typography>
      </div>

      <div>
        <Typography level="h3" gutterBottom>With gutterBottom</Typography>
        <Typography level="h4" gutterBottom>Subheading</Typography>
        <Typography level="bodyLarge">
          This text has proper spacing from the headings above, creating better visual hierarchy and readability.
        </Typography>
      </div>
    </div>
  ),
}

/** Typography with text wrapping options */
export const TextWrapping: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "Demonstrates the noWrap prop for controlling text overflow behavior.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 16, maxWidth: 300 }}>
      <div>
        <Typography level="labelMedium" gutterBottom>Normal wrapping (default):</Typography>
        <Typography level="bodyLarge">
          This is a long text that will wrap to multiple lines when it exceeds the container width.
        </Typography>
      </div>

      <div>
        <Typography level="labelMedium" gutterBottom>No wrap with ellipsis:</Typography>
        <Typography level="bodyLarge" noWrap>
          This is a long text that will be truncated with ellipsis when it exceeds the container width.
        </Typography>
      </div>
    </div>
  ),
}

/** Comprehensive typography showcase */
export const Showcase: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase demonstrating various typography levels, colors, and spacing in a realistic layout.",
      },
    },
  },
  render: () => (
    <div style={{ maxWidth: 800, margin: "0 auto" }}>
      <Typography level="displayMedium" align="center" gutterBottom>
        Apollo Design System
      </Typography>

      <Typography level="headlineMedium" gutterBottom>
        Typography Component
      </Typography>

      <Typography level="bodyLarge" gutterBottom>
        The Typography component provides a comprehensive set of text styles that follow the Apollo design system guidelines.
        It supports both legacy and Material Design 3 typography scales.
      </Typography>

      <Typography level="titleLarge" gutterBottom>
        Key Features
      </Typography>

      <Typography level="bodyMedium" gutterBottom>
        • Multiple typography levels for different content types
      </Typography>
      <Typography level="bodyMedium" gutterBottom>
        • Semantic color variants for different states and meanings
      </Typography>
      <Typography level="bodyMedium" gutterBottom>
        • Flexible alignment options for various layouts
      </Typography>
      <Typography level="bodyMedium" gutterBottom>
        • Built-in spacing and text overflow controls
      </Typography>

      <Typography level="titleMedium" color="primary" gutterBottom>
        Status Messages
      </Typography>

      <Typography level="bodyMedium" color="success" gutterBottom>
        ✓ Success: Your changes have been saved successfully.
      </Typography>

      <Typography level="bodyMedium" color="warning" gutterBottom>
        ⚠ Warning: Please review your input before proceeding.
      </Typography>

      <Typography level="bodyMedium" color="negative" gutterBottom>
        ✗ Error: Unable to process your request. Please try again.
      </Typography>
    </div>
  ),
}
