import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>,  <PERSON>po<PERSON> } from "@apollo/ui"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"

// meta
const meta = {
  title: "@apollo∕ui/Components/Navigation/Pagination",
  component: Pagination,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Pagination enables the user to select a specific page from a range of pages. It provides navigation for content that is spread across multiple pages.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Pagination } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="pagination-props">Props</h2>
          <ArgTypes />
          <h2 id="pagination-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Pagination component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloPagination-root",
                description: "Styles applied to the pagination root container",
                usageNotes: "Use for overall pagination styling and layout",
              },
              {
                cssClassName: ".ApolloPagination-full",
                description: "Styles applied to the full pagination view container",
                usageNotes: "Contains all pagination buttons in desktop view",
              },
              {
                cssClassName: ".ApolloPagination-compact",
                description: "Styles applied to the compact pagination view container",
                usageNotes: "Contains simplified pagination for mobile view",
              },
              {
                cssClassName: ".ApolloPagination-prevPageButton",
                description: "Styles applied to the previous page button",
                usageNotes: "Navigation button to go to previous page",
              },
              {
                cssClassName: ".ApolloPagination-nextPageButton",
                description: "Styles applied to the next page button",
                usageNotes: "Navigation button to go to next page",
              },
              {
                cssClassName: ".ApolloPagination-firstPageButton",
                description: "Styles applied to the first page button",
                usageNotes: "Navigation button to go to first page",
              },
              {
                cssClassName: ".ApolloPagination-lastPageButton",
                description: "Styles applied to the last page button",
                usageNotes: "Navigation button to go to last page",
              },
              {
                cssClassName: ".ApolloPagination-pageInfo",
                description: "Styles applied to the page info text",
                usageNotes: "Displays current page information in compact view",
              },
              {
                cssClassName: ".ApolloPagination-pageSize",
                description: "Styles applied to the page size selector container",
                usageNotes: "Contains the page size selection dropdown",
              },
            ]}
          />
          <h2 id="pagination-examples">Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  tags: ["autodocs"],
  argTypes: {
    count: { control: { type: "number", min: 1, max: 100 } },
    page: { control: { type: "number", min: 1 } },
    defaultPage: { control: { type: "number", min: 1 } },
    siblingCount: { control: { type: "number", min: 0, max: 5 } },
    boundaryCount: { control: { type: "number", min: 1, max: 5 } },
    showPrevPageButton: { control: { type: "boolean" } },
    showNextPageButton: { control: { type: "boolean" } },
    disabled: { control: { type: "boolean" } },
    disabledPrevPageButton: { control: { type: "boolean" } },
    disabledNextPageButton: { control: { type: "boolean" } },

    // new props
    pageSize: {
      control: { type: "text" },
      description: "Custom ReactNode element for page size selection",
    },
    pageSizeOptions: {
      control: { type: "object" },
      description: "Options for selecting items per page",
    },
    onPageSizeChange: {
      action: "pageSizeChanged",
      description: "Callback fired when page size changes",
    },
    displayType: {
      control: { type: "radio" },
      options: ["full", "compact"],
    },
    showFirstPageButton: { control: { type: "boolean" } },
    showLastPageButton: { control: { type: "boolean" } },
    disabledFirstPageButton: { control: { type: "boolean" } },
    disabledLastPageButton: { control: { type: "boolean" } },
  },
} satisfies Meta<typeof Pagination>

export default meta
type Story = StoryObj<typeof meta>

/* --- Stories --- */

// 1. Basic
export const Basic: Story = {
  args: {
    count: 10,
    defaultPage: 1,
  },
}

// 2. Controlled
export const Controlled: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const totalPages = 15

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          alignItems: "center",
        }}
      >
        <div
          style={{
            padding: "16px",
            background: "#f8f9fa",
            borderRadius: "8px",
            textAlign: "center",
            minWidth: "300px",
          }}
        >
          <Typography level="h5" gutterBottom>
            Search Results
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#666" }}>
            Showing page {currentPage} of {totalPages}
          </Typography>
        </div>

        <Pagination
          count={totalPages}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          siblingCount={2}
          boundaryCount={2}
        />

        <div
          style={{
            display: "flex",
            gap: "12px",
            flexWrap: "wrap",
            justifyContent: "center",
          }}
        >
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            First
          </Button>
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(Math.ceil(totalPages / 2))}
          >
            Middle
          </Button>
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            Last
          </Button>
        </div>
      </div>
    )
  },
}

// 3. Different configurations
export const DifferentConfigurations: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "32px" }}>
      <div>
        <Typography level="h5">Small Dataset (5 pages)</Typography>
        <Pagination count={5} defaultPage={3} />
      </div>

      <div>
        <Typography level="h5">Medium Dataset (25 pages)</Typography>
        <Pagination
          count={25}
          defaultPage={12}
          siblingCount={1}
          boundaryCount={2}
        />
      </div>

      <div>
        <Typography level="h5">Large Dataset (100 pages)</Typography>
        <Pagination
          count={100}
          defaultPage={50}
          siblingCount={2}
          boundaryCount={1}
        />
      </div>

      <div>
        <Typography level="h5">Extended Siblings (50 pages)</Typography>
        <Pagination
          count={50}
          defaultPage={25}
          siblingCount={3}
          boundaryCount={2}
        />
      </div>
    </div>
  ),
}

// 4. Button visibility
export const ButtonVisibility: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton
        showNextPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton={false}
        showNextPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton
        showNextPageButton={false}
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton={false}
        showNextPageButton={false}
      />
      <Pagination
        count={10}
        defaultPage={5}
        displayType="compact"
        showFirstPageButton
        showLastPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        displayType="compact"
        showFirstPageButton={false}
        showLastPageButton={false}
      />
    </div>
  ),
}

// 5. Disabled states
export const DisabledStates: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      <Pagination count={10} defaultPage={5} disabled />
      <Pagination count={10} defaultPage={5} disabledPrevPageButton />
      <Pagination count={10} defaultPage={5} disabledNextPageButton />
      <Pagination count={10} defaultPage={1} />
      <Pagination count={10} defaultPage={10} />
    </div>
  ),
}

export const DisplayTypeExample: Story = {
  render: () => {
    const [currentFull, setCurrentFull] = useState(5)
    const [currentCompact, setCurrentCompact] = useState(5)
    const totalPages = 20

    const containerStyle: React.CSSProperties = {
      display: "flex",
      flexDirection: "column",
      gap: "24px",
      alignItems: "center",
      width: "600px",
    }

    const blockStyle: React.CSSProperties = {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "8px",
      padding: "16px",
      border: "1px solid #ddd",
      borderRadius: "8px",
      width: "100%",
    }

    return (
      <div style={containerStyle}>
        <div style={blockStyle}>
          <Typography level="h5">Responsive Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentFull}
            onChange={(_, page) => setCurrentFull(page)}
            siblingCount={2}
            boundaryCount={1}
          />
        </div>
        <div style={blockStyle}>
          <Typography level="h5">Full Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentFull}
            onChange={(_, page) => setCurrentFull(page)}
            siblingCount={2}
            boundaryCount={1}
            displayType="full"
          />
        </div>

        <div style={blockStyle}>
          <Typography level="h5">Compact Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentCompact}
            onChange={(_, page) => setCurrentCompact(page)}
            siblingCount={1}
            boundaryCount={1}
            displayType="compact"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates Pagination in both `full` and `compact` display types.",
      },
    },
  },
}

// 6. Table pagination example with pageSize
export const TablePaginationExample: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const [itemsPerPage, setItemsPerPage] = useState(10)
    const totalItems = 247

    return (
      <div style={{ width: "800px" }}>
        <Pagination
          count={totalItems}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          siblingCount={1}
          boundaryCount={1}
          pageSize={itemsPerPage}
          pageSizeOptions={[5, 10, 25, 50]}
          onPageSizeChange={(size) => {
            setItemsPerPage(size)
            setCurrentPage(1)
          }}
        />
      </div>
    )
  },
}

// 7. Search results example
export const SearchResultsExample: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const totalResults = 1247
    const resultsPerPage = 20
    const totalPages = Math.ceil(totalResults / resultsPerPage)

    const startResult = (currentPage - 1) * resultsPerPage + 1
    const endResult = Math.min(currentPage * resultsPerPage, totalResults)

    return (
      <div style={{ width: "700px" }}>
        <Typography level="h3">Search Results</Typography>
        <Typography
          level="bodySmall"
          style={{ color: "#666", marginBottom: "24px" }}
        >
          About {totalResults.toLocaleString()} results
        </Typography>

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "16px",
            padding: "20px",
            background: "#fafafa",
            borderRadius: "8px",
          }}
        >
          <Typography level="bodyMedium" style={{ color: "#666" }}>
            Page {currentPage} of {totalPages} (showing {startResult}-
            {endResult})
          </Typography>

          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(_, page) => setCurrentPage(page)}
            siblingCount={2}
            boundaryCount={1}
          />

          <div style={{ display: "flex", gap: "16px" }}>
            <Button
              variant="text"
              size="small"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 10))}
              disabled={currentPage <= 10}
            >
              ← Previous 10
            </Button>
            <Button
              variant="text"
              size="small"
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 10))
              }
              disabled={currentPage > totalPages - 10}
            >
              Next 10 →
            </Button>
          </div>
        </div>
      </div>
    )
  },
}
