import React from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { <PERSON><PERSON>, Button, Typography } from "@apollo/ui"
import {
  CheckCircle,
  ClockCircle,
  CloseCircle,
  ExclamationCircle,
  InfoCircle,
  Warning,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Badge component
 *
 * The Badge component displays a small piece of information or status indicator.
 * It can be used standalone or as an overlay on other components to show counts,
 * statuses, or labels with various color themes and optional icons.
 *
 * Notes:
 * - Default color is "default";
 * - Available colors: "default" | "process" | "success" | "warning" | "error";
 * - Can be used standalone or with children for overlay positioning;
 * - Supports optional icons for enhanced visual communication;
 * - When used with children, badge appears as an indicator overlay.
 */
const meta = {
  title: "@apollo∕ui/Components/Data Display/Badge",
  component: Badge,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2210-18192&m=dev",
    },
    docs: {
      description: {
        component:
          "The Badge component renders a small status indicator or label with Apollo design system styling. Supports multiple color themes, optional icons, and can be used standalone or as an overlay on other components.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Badge } from "@apollo/ui"`} language="tsx" />
          <h2 id="badge-props">Props</h2>
          <ArgTypes />
          <h2 id="badge-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use clear, concise labels that communicate status or information effectively",
              "Choose appropriate colors that match the semantic meaning (success for positive states, error for problems, etc.)",
              "Use icons sparingly and only when they enhance understanding of the badge content",
              "For notification badges with children, ensure the badge doesn't obscure important content",
              "Keep badge text short - ideally single words or short phrases",
              "Use consistent color meanings across your application",
            ]}
          />
          <h2 id="badge-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Badge component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts of the component."
            data={[
              {
                cssClassName: ".ApolloBadge-root",
                description: "Styles applied to the root badge element",
                usageNotes: "Use for overall badge styling, positioning, and base properties",
              },
              {
                cssClassName: ".ApolloBadge-wrapper",
                description: "Styles applied to the wrapper element when children are provided",
                usageNotes: "Use for positioning the badge relative to its children",
              },
              {
                cssClassName: ".ApolloBadge-icon",
                description: "Styles applied to the icon element when present",
                usageNotes: "Use for customizing icon appearance and spacing",
              },
              {
                cssClassName: ".ApolloBadge-label",
                description: "Styles applied to the label text element",
                usageNotes: "Use for customizing text styling and typography",
              },
            ]}
          />
          <h2 id="badge-examples">Examples</h2>
          <Stories title="" />
          <h2 id="badge-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <Badge label="Approved" color="success" />
                      <Badge label="Pending" color="warning" />
                      <Badge label="Rejected" color="error" />
                    </div>
                  ),
                  description: "Use colors that match semantic meaning",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <Badge label="Approved" color="error" />
                      <Badge label="Pending" color="success" />
                      <Badge label="Rejected" color="warning" />
                    </div>
                  ),
                  description:
                    "Don't use colors that conflict with the semantic meaning",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <Badge label="Active" color="process" icon={<ClockCircle />} />
                      <Badge label="Complete" color="success" icon={<CheckCircle />} />
                    </div>
                  ),
                  description:
                    "Use icons that support and clarify the badge's meaning",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <Badge label="Active" color="process" icon={<CloseCircle />} />
                      <Badge label="Complete" color="success" icon={<Warning />} />
                    </div>
                  ),
                  description:
                    "Don't use icons that conflict with or confuse the badge's meaning",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <Badge label="New" />
                      <Badge label="5" />
                      <Badge label="Beta" />
                    </div>
                  ),
                  description:
                    "Keep badge text short and clear",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <Badge label="This is a very long badge text that doesn't fit well" />
                    </div>
                  ),
                  description:
                    "Don't use long text that makes badges difficult to read",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
                      <Badge label="New" color="success" />
                      <Badge label="5" color="error" />
                      <Badge label="Beta" color="warning" />
                      <Badge label="Status" color="process" />
                    </div>
                  ),
                  description:
                    "Use appropriate badge sizing and styling that clearly indicates non-interactive status",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "flex-start", flexWrap: "wrap" }}>
                      <Badge label="Delete" color="error" />
                      <Button>Edit</Button>
                    </div>
                  ),
                  description:
                    "Don’t confuse badges with a button.",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description: "The text content to display inside the badge.",
      table: {
        type: { summary: "string" },
      },
    },
    color: {
      control: { type: "radio" },
      options: ["default", "process", "success", "warning", "error"],
      description: "Color theme of the badge. Default is 'default'.",
      table: {
        type: { summary: '"default" | "process" | "success" | "warning" | "error"' },
        defaultValue: { summary: "default" },
      },
    },
    icon: {
      control: false,
      description:
        "Optional icon element to display alongside the label.",
      table: { type: { summary: "ReactNode" } },
    },
    children: {
      control: false,
      description: "When provided, the badge will be positioned as an overlay on the children.",
      table: { type: { summary: "ReactNode" } },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes to apply to the badge.",
      table: { type: { summary: "string" } },
    },
  },
  args: {
    label: "Badge",
    color: "default",
  },
} satisfies Meta<typeof Badge>

export default meta

type Story = StoryObj<typeof Badge>

/** Default Badge (demonstrates default color) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Badge with default settings. The component defaults to color 'default' and displays as a standalone badge.",
      },
    },
  },
  args: {
    label: "Badge",
  },
}

/** Badge with different colors (default, process, success, warning, error) */
export const Colors: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available color themes: default, process, success, warning, and error. Each color conveys different semantic meaning.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
      <Badge {...args} label="Default" color="default" />
      <Badge {...args} label="Process" color="process" />
      <Badge {...args} label="Success" color="success" />
      <Badge {...args} label="Warning" color="warning" />
      <Badge {...args} label="Error" color="error" />
    </div>
  ),
}

/** Badge with icons */
export const WithIcons: Story = {
  parameters: {
    docs: {
      description: {
        story: "Badges with icons that enhance the meaning and visual appeal of the status indicators.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Badge {...args} label="Info" color="default" icon={<InfoCircle />} />
      <Badge {...args} label="Active" color="process" icon={<ClockCircle />} />
      <Badge {...args} label="Complete" color="success" icon={<CheckCircle />} />
      <Badge {...args} label="Warning" color="warning" icon={<ExclamationCircle />} />
      <Badge {...args} label="Error" color="error" icon={<CloseCircle />} />
    </div>
  ),
}



/** Status indicators showcase */
export const StatusIndicators: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A comprehensive showcase of badge usage for different status indicators commonly used in applications.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Order Status</Typography>
          <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
            <Badge label="Pending" color="warning" icon={<ClockCircle />} />
            <Badge label="Processing" color="process" />
            <Badge label="Shipped" color="success" icon={<CheckCircle />} />
            <Badge label="Delivered" color="success" />
            <Badge label="Cancelled" color="error" icon={<CloseCircle />} />
          </div>
        </div>
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">User Status</Typography>
          <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
            <Badge label="Online" color="success" />
            <Badge label="Away" color="warning" />
            <Badge label="Busy" color="error" />
            <Badge label="Offline" color="default" />
          </div>
        </div>
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Priority Levels</Typography>
          <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
            <Badge label="Low" color="default" />
            <Badge label="Medium" color="warning" />
            <Badge label="High" color="error" icon={<ExclamationCircle />} />
            <Badge label="Critical" color="error" />
          </div>
        </div>
      </div>
    )
  },
}

/** Interactive badge examples */
export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story: "Examples of badges used in interactive contexts, such as filters, tags, or actionable status indicators.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 24,
          alignItems: "start",
        }}
      >
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Filter Tags</Typography>
          <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
            <Badge label="Category: Electronics" color="process" />
            <Badge label="Price: $100-$500" color="warning" />
            <Badge label="In Stock" color="success" />
            <Badge label="Free Shipping" color="default" />
          </div>
        </div>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Feature Flags</Typography>
          <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
            <Badge label="Beta" color="warning" />
            <Badge label="New" color="success" />
            <Badge label="Experimental" color="error" />
            <Badge label="Coming Soon" color="process" />
          </div>
        </div>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Version Tags</Typography>
          <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
            <Badge label="v1.0.0" color="success" />
            <Badge label="v2.0.0-beta" color="warning" />
            <Badge label="v3.0.0-alpha" color="error" />
            <Badge label="Latest" color="process" />
          </div>
        </div>
      </div>
    )
  },
}

/** Size and spacing variations */
export const Variations: Story = {
  parameters: {
    docs: {
      description: {
        story: "Different badge configurations showing various text lengths and spacing considerations.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 16,
        alignItems: "start",
      }}
    >
      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Short Labels</Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Badge {...args} label="1" color="error" />
          <Badge {...args} label="99" color="warning" />
          <Badge {...args} label="New" color="success" />
          <Badge {...args} label="Hot" color="error" />
        </div>
      </div>
      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Medium Labels</Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Badge {...args} label="Pending" color="warning" />
          <Badge {...args} label="Complete" color="success" />
          <Badge {...args} label="In Progress" color="process" />
          <Badge {...args} label="Cancelled" color="error" />
        </div>
      </div>
      <div>
        <Typography level="bodyLarge" style={{ marginBottom: 8 }}>With Icons</Typography>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Badge {...args} label="5 min" color="process" icon={<ClockCircle />} />
          <Badge {...args} label="Verified" color="success" icon={<CheckCircle />} />
          <Badge {...args} label="Alert" color="warning" icon={<ExclamationCircle />} />
          <Badge {...args} label="Failed" color="error" icon={<CloseCircle />} />
        </div>
      </div>
    </div>
  ),
}

/** Badge as notification indicator */
export const NotificationBadge: Story = {
  parameters: {
    docs: {
      description: {
        story: "Badge used as a notification indicator overlay on other components. The badge appears positioned relative to its children.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        gap: 32,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Badge {...args} label="3" style={{ borderRadius: '999px', backgroundColor: 'var(--apl-alias-color-error-error)', color: 'var(--apl-alias-color-static-text-icon-text-icon-light)' }}>
        <div
          style={{
            width: 50,
            height: 50,
            backgroundColor: "#f0f0f0",
            borderRadius: 8,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "1px solid #ddd",
          }}
        />
        
      </Badge>
      <Badge {...args} label="New" color="success">
        <div
          style={{
            width: 50,
            height: 50,
            backgroundColor: "#f0f0f0",
            borderRadius: 8,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "1px solid #ddd",
          }}
        />
      </Badge>
      <Badge {...args} label="99+" color="warning">
        <div
          style={{
            width: 50,
            height: 50,
            backgroundColor: "#f0f0f0",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "1px solid #ddd",
          }}
        />
      </Badge>
    </div>
  ),
}