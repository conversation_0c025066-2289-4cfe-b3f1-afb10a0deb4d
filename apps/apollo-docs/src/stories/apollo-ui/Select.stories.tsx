import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import {
  Button,
  Checkbox,
  IconButton,
  Input,
  Modal,
  Select,
  Typography,
} from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"
import { Controller, useForm } from "react-hook-form"
import { z } from "zod"

const countryOptions = [
  { label: "United States", value: "us" },
  { label: "Canada", value: "ca" },
  { label: "Mexico", value: "mx" },
  { label: "United Kingdom", value: "uk" },
  { label: "France", value: "fr" },
  { label: "Germany", value: "de" },
  { label: "Italy", value: "it" },
  { label: "Spain", value: "es" },
  { label: "Portugal", value: "pt" },
  { label: "Netherlands", value: "nl" },
  { label: "Belgium", value: "be" },
  { label: "Sweden", value: "se" },
  { label: "Norway", value: "no" },
  { label: "Denmark", value: "dk" },
  { label: "Finland", value: "fi" },
  { label: "Iceland", value: "is" },
  { label: "Ireland", value: "ie" },
  { label: "Switzerland", value: "ch" },
  { label: "Austria", value: "at" },
  { label: "Poland", value: "pl" },
  { label: "Czech Republic", value: "cz" },
  { label: "Hungary", value: "hu" },
  { label: "Slovakia", value: "sk" },
  { label: "Romania", value: "ro" },
  { label: "Bulgaria", value: "bg" },
  { label: "Greece", value: "gr" },
  { label: "Turkey", value: "tr" },
  { label: "Russia", value: "ru" },
  { label: "Ukraine", value: "ua" },
  { label: "Israel", value: "il" },
  { label: "Saudi Arabia", value: "sa" },
  { label: "United Arab Emirates", value: "ae" },
  { label: "Qatar", value: "qa" },
  { label: "Kuwait", value: "kw" },
  { label: "Oman", value: "om" },
  { label: "Japan", value: "jp" },
  { label: "South Korea", value: "kr" },
  { label: "China", value: "cn" },
  { label: "India", value: "in" },
  { label: "Indonesia", value: "id" },
  { label: "Thailand", value: "th" },
  { label: "Vietnam", value: "vn" },
  { label: "Philippines", value: "ph" },
  { label: "Malaysia", value: "my" },
  { label: "Singapore", value: "sg" },
  { label: "Australia", value: "au" },
  { label: "New Zealand", value: "nz" },
  { label: "South Africa", value: "za" },
  { label: "Egypt", value: "eg" },
  { label: "Nigeria", value: "ng" },
  { label: "Kenya", value: "ke" },
  { label: "Morocco", value: "ma" },
  { label: "Tunisia", value: "tn" },
  { label: "Brazil", value: "br" },
  { label: "Argentina", value: "ar" },
  { label: "Chile", value: "cl" },
  { label: "Colombia", value: "co" },
  { label: "Peru", value: "pe" },
  { label: "Uruguay", value: "uy" },
  { label: "Venezuela", value: "ve" },
]

/**
 * Select component
 *
 * The Select component lets users choose a single value from a list of options.
 * It supports label, helper text, required indicator, disabled and error states,
 * full-width layout, small/medium sizes, placeholders, and controlled/uncontrolled usage.
 *
 * Notes:
 * - Default size is "medium"
 * - Use <Select.Option label="..." value={...} /> for options
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Select",
  component: Select,
  subcomponents: { Option: Select.Option },
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2520-6311&m=dev",
    },
    docs: {
      description: {
        component:
          "The Select component renders a dropdown for choosing a single value. It composes Field/Input internally to provide consistent label, helper text, and validation styling.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Select } from "@apollo/ui"`} language="tsx" />

          <h2 id="select-props">Props</h2>
          <ArgTypes />

          <h2 id="select-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Provide clear, concise labels to help users understand their choices.",
              "Ensure the default selection is meaningful to prevent unnecessary user actions.",
              "Use “Select” as a placeholder option only if there’s no logical default option.",
              "Keep option labels concise and descriptive."
            ]}
          />

          <h2 id="select-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide a <code>label</code> and use{" "}
                <code>required</code> where needed.
              </>,
              <>
                Pair <code>error</code> with <code>helperText</code> to describe
                the issue.
              </>,
              <>
                Use <code>placeholder</code> to guide users, but avoid relying
                on it as the sole label.
              </>,
              <>
                Keyboard users can open with Enter/Space and navigate with arrow
                keys.
              </>,
            ]}
          />
          <h2 id="select-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Select component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloSelect-fieldRoot",
                description: "Styles applied to the field wrapper element",
                usageNotes: "Use for overall field styling including label and helper text positioning",
              },
              {
                cssClassName: ".ApolloSelect-triggerContainer",
                description: "Styles applied to the trigger container",
                usageNotes: "Contains the select trigger and handles width and positioning",
              },
              {
                cssClassName: ".ApolloSelect-trigger",
                description: "Styles applied to the select trigger element",
                usageNotes: "The clickable element that opens the dropdown",
              },
              {
                cssClassName: ".ApolloSelect-input",
                description: "Styles applied to the input element (when searchable)",
                usageNotes: "Used for searchable select variants with text input",
              },
              {
                cssClassName: ".ApolloSelect-positioner",
                description: "Styles applied to the dropdown positioner",
                usageNotes: "Handles dropdown positioning and collision avoidance",
              },
              {
                cssClassName: ".ApolloSelect-popup",
                description: "Styles applied to the dropdown popup container",
                usageNotes: "Contains the dropdown menu with options",
              },
              {
                cssClassName: ".ApolloSelect-option",
                description: "Styles applied to individual option elements",
                usageNotes: "Base styling for dropdown options",
              },
              {
                cssClassName: ".ApolloSelect-menuItem",
                description: "Styles applied to menu item components",
                usageNotes: "Enhanced styling for interactive menu items with selection states",
              },
            ]}
          />
          <h2 id="select-examples">Examples</h2>
          <Stories title="" />
          <h2 id="select-dos-donts">Do’s and Don’ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Select label="Category" placeholder="Choose one" fullWidth>
                        <Select.Option
                          label="Electronics"
                          value="electronics"
                        />
                        <Select.Option label="Groceries" value="groceries" />
                      </Select>
                    </div>
                  ),
                  description:
                    "Use a clear label and concise placeholder to guide users.",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Select
                        label="Choose from a very long and descriptive message that belongs in helper text"
                        placeholder="Choose one"
                        fullWidth
                      >
                        <Select.Option
                          label="Electronics"
                          value="electronics"
                        />
                        <Select.Option label="Groceries" value="groceries" />
                      </Select>
                    </div>
                  ),
                  description:
                    "Avoid placing instructions in the label; use helperText instead.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Select label="Category" placeholder="Choose one" fullWidth>
                        <Select.Option label="None" value="-" />
                        <Select.Option
                          label="Electronics"
                          value="electronics"
                        />
                        <Select.Option label="Groceries" value="groceries" />
                      </Select>
                    </div>
                  ),
                  description:
                    "In case the user wants to reset the selection to an empty value, an additional choice representing an empty option must be added to the list.",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Select label="Choose country" placeholder="Choose one" fullWidth>
                        {countryOptions.map((o) => (
                          <Select.Option
                            key={o.value}
                            label={o.label}
                            value={o.value}
                          />
                        ))}
                      </Select>
                    </div>
                  ),
                  description:
                    "Avoid using Select if the list is too long and needs search functionality",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    size: {
      control: { type: "radio" },
      options: ["small", "medium"],
      description: "Size of the input.",
      table: {
        type: { summary: '"small" | "medium"' },
        defaultValue: { summary: "medium" },
      },
    },
    label: { control: "text", description: "Field label." },
    labelDecorator: {
      control: false,
      description: "Element displayed next to the label (ReactNode).",
      table: { type: { summary: "ReactNode" } },
    },
    helperText: {
      control: "text",
      description: "Helper or error text below the field.",
    },
    helperTextDecorator: {
      control: false,
      description: "Element displayed next to helper text (ReactNode).",
      table: { type: { summary: "ReactNode" } },
    },
    fullWidth: {
      control: "boolean",
      description: "Stretch to the container width.",
    },
    required: {
      control: "boolean",
      description: "Mark the field as required.",
    },
    error: { control: "boolean", description: "Error state (affects styles)." },
    disabled: { control: "boolean", description: "Disable the field." },
    placeholder: {
      control: "text",
      description: "Placeholder text when no value is selected.",
    },

    value: {
      control: false,
      description: "Controlled value.",
      table: { type: { summary: "any" } },
    },
    defaultValue: {
      control: false,
      description: "Uncontrolled default value.",
      table: { type: { summary: "any" } },
    },
    onChange: {
      control: false,
      description: "Callback fired when value changes: (value: any) => void",
      table: { type: { summary: "(value: any) => void" } },
    },
    fieldProps: {
      control: false,
      description: "Props for internal Field component.",
    },
    ref: { control: false, table: { type: { summary: "Ref<HTMLElement>" } } },
    children: { control: false, description: "Select.Option elements." },
  },
  args: {
    placeholder: "Select an option",
    fullWidth: false,
    disabled: false,
    error: false,
    required: false,
    size: "medium",
  },
} satisfies Meta<typeof Select>

export default meta

type Story = StoryObj<typeof Select>

const baseOptions = [
  { label: "Electronics — Laptops", value: "laptops" },
  { label: "Home & Kitchen — Cookware", value: "cookware" },
  { label: "Clothing — Men's Jackets", value: "mens-jackets" },
  { label: "Beauty & Personal Care", value: "beauty" },
]

/** Default Select (basic usage) */
export const Overview: Story = {
  args: { label: "Label", helperText: "Helper text" },
  render: (args) => (
    <div style={{ minWidth: 260 }}>
      <Select {...args}>
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
    </div>
  ),
}

/** Sizes: small and medium */
export const Sizes: Story = {
  render: (args) => (
    <div style={{ display: "flex", gap: 16 }}>
      <div style={{ minWidth: 220 }}>
        <Select {...args} size="small" label="Small" placeholder="Small size">
          {baseOptions.map((o) => (
            <Select.Option key={o.value} label={o.label} value={o.value} />
          ))}
        </Select>
      </div>
      <div style={{ minWidth: 220 }}>
        <Select
          {...args}
          size="medium"
          label="Medium"
          placeholder="Medium size"
        >
          {baseOptions.map((o) => (
            <Select.Option key={o.value} label={o.label} value={o.value} />
          ))}
        </Select>
      </div>
    </div>
  ),
}

/** Full width layout */
export const FullWidth: Story = {
  parameters: { layout: "padded" },
  args: { label: "Label", helperText: "Helper text", fullWidth: true },
  render: (args) => (
    <Select {...args}>
      {baseOptions.map((o) => (
        <Select.Option key={o.value} label={o.label} value={o.value} />
      ))}
    </Select>
  ),
}

/** Common states: default, disabled, error, decorators */
export const States: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Side-by-side comparison of common states: default, with decorators, disabled, error.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(2, minmax(260px, 1fr))",
        gap: 20,
        alignItems: "end",
      }}
    >
      <Select {...args} label="Label" helperText="Helper text">
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        label="Label"
        labelDecorator={<InfoCircle size={12} />}
        helperText="Helper text"
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="With decorators"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select {...args} disabled placeholder="Disabled">
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        disabled
        defaultValue={baseOptions[0].value}
        placeholder="Disabled with value"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        error
        label="Label"
        helperText="This field is required"
        placeholder="Error state"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        error
        label="Label"
        helperText="This field is required"
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="Error with decorator"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
    </div>
  ),
}

/** Select with label */
export const Label: Story = {
  parameters: {
    docs: {
      description: {
        story: "Select with label and label decorator.",
      },
    },
  },
  render: () => {
    function Demo() {
      const [open, setOpen] = useState(false)
      const close = () => setOpen(false)
      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "center",
          }}
        >
          <Typography level="bodyLarge">Default</Typography>
          <Select label="Category" placeholder="Select a category" fullWidth>
            {baseOptions.map((o) => (
              <Select.Option key={o.value} label={o.label} value={o.value} />
            ))}
          </Select>
          <Typography level="bodyLarge">Label Decorator (Clickable)</Typography>
          <Select
            label="Category"
            placeholder="Select a category"
            labelDecorator={
              <IconButton
                type="button"
                onClick={() => setOpen(true)}
                style={{
                  padding: 0,
                  background: "none",
                  height: "fit-content",
                  minHeight: "fit-content",
                  width: "fit-content",
                  minWidth: "fit-content",
                }}
                aria-label="More info"
                size="small"
              >
                <InfoCircle size={12} />
              </IconButton>
            }
            fullWidth
          >{baseOptions.map((o) => (
              <Select.Option key={o.value} label={o.label} value={o.value} />
            ))}
          </Select>

          <Modal.Root
            open={open}
            onOpenChange={(o) => setOpen(!!o)}
            dismissible
          >
            <Modal.Header>Modal Title</Modal.Header>
            <Modal.Content>
              <div>
                Once upon a time, there was a forest where plenty of birds lived
                and built their nests on the trees.
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button onClick={close}>Button</Button>
            </Modal.Footer>
            <Modal.CloseButton />
          </Modal.Root>
        </div>
      )
    }
    return <Demo />
  },
}

/** Select with helper text */
export const HelperText: Story = {
  parameters: {
    docs: {
      description: {
        story: "Select with helper text and helper text decorator.",
      },
    },
  },
  render: () => {
    function Demo() {
      const [open, setOpen] = useState(false)
      const close = () => setOpen(false)
      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "center",
          }}
        >
          <Typography level="bodyLarge">Default</Typography>
          <Select
            label="Category"
            helperText="Helper text"
            placeholder="Select a category"
            fullWidth
          >
            {baseOptions.map((o) => (
              <Select.Option key={o.value} label={o.label} value={o.value} />
            ))}
          </Select>
          <Typography level="bodyLarge">With long helper text</Typography>
          <Select
            label="Category"
            placeholder="Select a category"
            error
            helperText={
              <Typography level="labelLarge" color="danger">
                You can not use the name
                <Typography
                  level="labelLarge"
                  color="danger"
                  onClick={() => setOpen(true)}
                  style={{
                    cursor: "pointer",
                    textDecoration: "underline",
                    marginLeft: "4px",
                  }}
                >
                  show detail
                </Typography>
              </Typography>
            }
            fullWidth
          >
            {baseOptions.map((o) => (
              <Select.Option key={o.value} label={o.label} value={o.value} />
            ))}
          </Select>
          <Modal.Root
            open={open}
            onOpenChange={(o) => setOpen(!!o)}
            dismissible
          >
            <Modal.Header>Modal Title</Modal.Header>
            <Modal.Content>
              <div>
                Once upon a time, there was a forest where plenty of birds lived
                and built their nests on the trees.
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button onClick={close}>Button</Button>
            </Modal.Footer>
            <Modal.CloseButton />
          </Modal.Root>
        </div>
      )
    }
    return <Demo />
  },
}

/** Select with validation */
export const Validation: Story = {
  parameters: {
    docs: {
      description: {
        story: "Select with label, helper text, and validation states.",
      },
    },
  },
  render: (args) => (
    <div style={{ minWidth: 260 }}>
      <Select {...args}>
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
    </div>
  ),
  args: {
    label: "Category",
    required: true,
    helperText: "Validate me",
    error: true,
  },
}

/** Controlled example */
export const Controlled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Controlled example: use state to manage the current value and update via onChange.",
      },
    },
  },
  render: (args) => {
    const [value, setValue] = useState<string | undefined>("mens-jackets")
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 8,
          minWidth: 300,
        }}
      >
        <Typography level="bodyLarge">{`Selected: ${value ?? "(none)"}`}</Typography>
        <Select {...args} value={value} onChange={setValue}>
          {baseOptions.map((o) => (
            <Select.Option key={o.value} label={o.label} value={o.value} />
          ))}
        </Select> 
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Button size="small" onClick={() => setValue('cookware')}>
           Home & Kitchen — Cookware
          </Button>
          <Button size="small" onClick={() => setValue("beauty")}>
            Beauty & Personal Care
          </Button>
          <Button size="small" variant="outline" onClick={() => setValue(undefined)}>
            Clear
          </Button>
        </div>
      </div>
    )
  },
  args: { label: "Category", fullWidth: true },
}

/** Form Integration with react-hook-form */
export const FormIntegration: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "Demonstrates Select integrated within a realistic form using react-hook-form and zod validation. Shows error states, form submission, and accessibility patterns.",
      },
    },
  },
  render: () => {
    // Form validation schema for the form integration story
    const formSchema = z.object({
      email: z.string().email("Please enter a valid email address"),
      category: z.string().min(1, "Please select a category"),
      newsletter: z.boolean(),
    })

    type FormData = z.infer<typeof formSchema>

    const {
      control,
      register,
      handleSubmit,
      formState: { errors, isSubmitting },
      reset,
    } = useForm<FormData>({
      resolver: zodResolver(formSchema),
      defaultValues: {
        email: "",
        category: "",
        newsletter: false,
      },
    })

    const onSubmit = async (data: FormData) => {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
      alert(`Form submitted successfully!\n\n${JSON.stringify(data, null, 2)}`)
    }

    const categoryOptions = [
      { label: "General Inquiry", value: "general" },
      { label: "Technical Support", value: "support" },
      { label: "Sales Question", value: "sales" },
      { label: "Partnership", value: "partnership" },
      { label: "Feedback", value: "feedback" },
    ]

    return (
      <div style={{ maxWidth: 600, margin: "0 auto" }}>
        <Typography level="titleLarge" style={{ marginBottom: 24 }}>
          Contact Form
        </Typography>
        <form
          onSubmit={handleSubmit(onSubmit)}
          style={{ display: "flex", flexDirection: "column", gap: 20 }}
        >
          {/* Email */}
          <Input
            {...register("email")}
            type="email"
            label="Email Address"
            placeholder="Enter your email"
            required
            error={!!errors.email}
            helperText={errors.email?.message}
            fullWidth
          />

          {/* Category Select */}
          <Controller
            name="category"
            control={control}
            render={({ field }) => (
              <Select
                {...field}
                label="Category"
                placeholder="Select a category"
                required
                error={!!errors.category}
                helperText={errors.category?.message}
                fullWidth
              >
                {categoryOptions.map((category) => (
                  <Select.Option
                    key={category.value}
                    label={category.label}
                    value={category.value}
                  />
                ))}
              </Select>
            )}
          />

          {/* Newsletter checkbox */}
          <Controller
            name="newsletter"
            control={control}
            render={({ field: { value, onChange, ...field } }) => (
              <Checkbox
                {...field}
                checked={value}
                onChange={(checked) => onChange(checked)}
                label="Subscribe to our newsletter"
              />
            )}
          />

          {/* Form actions */}
          <div
            style={{
              display: "flex",
              gap: 12,
              justifyContent: "flex-end",
              marginTop: 8,
            }}
          >
            <Button
              type="button"
              variant="outline"
              onClick={() => reset()}
              disabled={isSubmitting}
            >
              Reset
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </form>
      </div>
    )
  },
}

/** Dynamic Value Types with complex objects */
export const DynamicValueTypes: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates Select working with complex data types. Shows type-safe handling of different value types beyond simple strings and numbers.",
      },
    },
  },
  render: () => {
    type Product = {
      id: string
      name: string
      price: number
      category: string
      inStock: boolean
    }
    // Product objects
    const products: { label: string; value: Product | string }[] = [
      { label: "None", value: "-" },
      {
        label: "Laptop Pro",
        value: {
          id: "prod-1",
          name: "Laptop Pro",
          price: 1299.99,
          category: "Electronics",
          inStock: true,
        },
      },
      {
        label: "Wireless Mouse",
        value: {
          id: "prod-2",
          name: "Wireless Mouse",
          price: 29.99,
          category: "Accessories",
          inStock: false,
        },
      },
      {
        label: "Monitor 4K",
        value: {
          id: "prod-3",
          name: "Monitor 4K",
          price: 399.99,
          category: "Electronics",
          inStock: true,
        },
      },
    ]

    // State for different value types
    const [selectedProduct, setSelectedProduct] = useState<
      Product | string | undefined
    >()

    return (
      <div>
        <Typography level="titleMedium" style={{ marginBottom: 16 }}>
          Product Objects (Custom Serialization)
        </Typography>
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Select
            label="Product"
            placeholder="Choose a product"
            value={selectedProduct}
            onChange={setSelectedProduct}
            fullWidth
          >
            {products.map((product) => (
              <Select.Option
                key={
                  typeof product.value === "string"
                    ? product.value
                    : product.value.id
                }
                label={
                  typeof product.value === "string"
                    ? product.label
                    : `${product.value.name} - $${product.value.price} ${!product.value.inStock ? "(Out of Stock)" : ""}`
                }
                value={product.value}
              />
            ))}
          </Select>
          <div
            style={{
              padding: 16,
              backgroundColor:
                "var(--apl-alias-color-background-and-surface-surface)",
              borderRadius: 8,
              fontSize: 14,
            }}
          >
            <Typography level="labelLarge">Selected Product:</Typography>
            <pre style={{ margin: "8px 0 0 0", fontSize: 12 }}>
              {selectedProduct
                ? JSON.stringify(selectedProduct, null, 2)
                : "None"}
            </pre>
          </div>
        </div>
      </div>
    )
  },
}
