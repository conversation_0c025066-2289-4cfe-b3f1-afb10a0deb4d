import React, { useCallback, useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Button, Typography } from "@apollo/ui"
import {
  ArrowRight,
  Download,
  Heart,
  InfoCircle,
  Loading3Quarters,
  Search,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Button component
 *
 * The Button component provides a styled, accessible button with multiple variants,
 * sizes, colors, decorators, and support for link functionality.
 *
 * Notes:
 * - Default variant is "filled";
 * - Default size is "large";
 * - Default color is "primary";
 * - Available variants: "filled" | "outline" | "text";
 * - Available sizes: "large" | "small";
 * - Available colors: "primary" | "negative".
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Button",
  component: Button,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2210-18192&m=dev",
    },
    docs: {
      description: {
        component:
          "The Button component renders a button with Apollo design system styling. Supports multiple variants, sizes, colors, and can be rendered as a link when href is provided.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Button } from "@apollo/ui"`} language="tsx" />
          <h2 id="button-props">Props</h2>
          <ArgTypes />
          <h2 id="button-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use clear, action-oriented labels that describe what the button does",
              "Primary buttons should be used for the main action on a page",
              "Use outline buttons for secondary actions",
              "Use text buttons for tertiary actions or when you need minimal visual weight",
              "Negative color should be used for destructive actions",
              "Use startDecorator and endDecorator to add icons that support the button's purpose",
            ]}
          />
          <h2 id="button-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive button text that clearly indicates
                the action that will be performed when the button is activated.
              </>,
              <>
                Use the <code>disabled</code> prop to disable buttons that are
                not currently actionable, ensuring they are not focusable.
              </>,
              <>
                When using icon decorators, ensure the button text provides
                sufficient context.
              </>,
              <>
                For text link buttons using <code>variant="text"</code> <code>href</code>, 
                and ensure the link destination is clear from the button text or provide additional context.
              </>,
              <>
                Use appropriate color variants to convey meaning - negative for
                destructive actions, primary for main actions.
              </>,
              <>
                Ensure sufficient color contrast between button text and
                background in all states (default, hover, focus, active).
              </>,
            ]}
          />
          <h2 id="button-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Button component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloButton-root",
                description: "Styles applied to the root button element",
                usageNotes: "Use for overall button styling, positioning, and base properties",
              },
            ]}
          />
          <h2 id="button-examples">Examples</h2>
          <Stories title="" />
          <h2 id="button-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <Button>Save Changes</Button>
                      <Button variant="outline">Cancel</Button>
                    </div>
                  ),
                  description: "Use clear, action-oriented labels",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <Button>Click Here</Button>
                      <Button variant="outline">Button</Button>
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't describe the action",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <Button startDecorator={<Download size={16} />}>
                        Download Report
                      </Button>
                    </div>
                  ),
                  description:
                    "Use icons that support and clarify the button's purpose",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <Button startDecorator={<Heart size={16} />}>
                        Download Report
                      </Button>
                    </div>
                  ),
                  description:
                    "Don't use icons that conflict with or confuse the button's purpose",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <Button>Save</Button>
                      <Button variant="outline">Cancel</Button>
                      <Button variant="text">Skip</Button>
                    </div>
                  ),
                  description:
                    "Use appropriate hierarchy - filled for primary, outline for secondary, text for tertiary actions",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16 }}>
                      <Button>Save</Button>
                      <Button>Cancel</Button>
                      <Button>Skip</Button>
                    </div>
                  ),
                  description:
                    "Don't use the same variant for all actions - it creates confusion about priority",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    variant: {
      control: { type: "radio" },
      options: ["filled", "outline", "text"],
      description: "Visual style variant of the button. Default is 'filled'.",
      table: {
        type: { summary: '"filled" | "outline" | "text"' },
        defaultValue: { summary: "filled" },
      },
    },
    size: {
      control: { type: "radio" },
      options: ["large", "small"],
      description: "Visual size of the button. Default is 'large'.",
      table: {
        type: { summary: '"large" | "small"' },
        defaultValue: { summary: "large" },
      },
    },
    color: {
      control: { type: "radio" },
      options: ["primary", "negative"],
      description: "Color theme of the button. Default is 'primary'.",
      table: {
        type: { summary: '"primary" | "negative"' },
        defaultValue: { summary: "primary" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the button.",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Stretches the button to the full width of its container.",
    },
    startDecorator: {
      control: false,
      description:
        "Element displayed at the start of the button (typically an icon).",
      table: { type: { summary: "ReactNode" } },
    },
    endDecorator: {
      control: false,
      description:
        "Element displayed at the end of the button (typically an icon).",
      table: { type: { summary: "ReactNode" } },
    },
    href: {
      control: { type: "text" },
      description:
        "When provided, renders the button as an anchor element with this URL.",
      table: { type: { summary: "string" } },
    },
    onClick: {
      control: false,
      description: "Callback fired when the button is clicked.",
      table: {
        type: {
          summary: "(event: React.MouseEvent<HTMLButtonElement>) => void",
        },
      },
    },
    children: {
      control: { type: "text" },
      description: "The content of the button.",
      table: { type: { summary: "ReactNode" } },
    },
  },
  args: {
    children: "Button",
    variant: "filled",
    size: "large",
    color: "primary",
    disabled: false,
    fullWidth: false,
  },
} satisfies Meta<typeof Button>

export default meta

type Story = StoryObj<typeof Button>

/** Default Button (demonstrates default filled variant) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Button with default settings. The component defaults to variant 'filled', size 'large', and color 'primary'.",
      },
    },
  },
  args: {
    children: "Button",
  },
}

/** Button with different variants (filled, outline, text) */
export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases all available button variants: filled (default), outline, and text.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Button {...args} variant="filled">
        Filled
      </Button>
      <Button {...args} variant="outline">
        Outline
      </Button>
      <Button {...args} variant="text">
        Text
      </Button>
    </div>
  ),
}

/** Button with different sizes (large, small) */
export const Sizes: Story = {
  parameters: {
    docs: {
      description: {
        story: "Showcases both available sizes: large (default) and small.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Button {...args} size="large">
        Large
      </Button>
      <Button {...args} size="small">
        Small
      </Button>
    </div>
  ),
}

/** Button with different colors (primary, negative) */
export const Colors: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Showcases both available color themes: primary (default) and negative.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Button {...args} color="primary">
        Primary
      </Button>
      <Button {...args} color="negative">
        Negative
      </Button>
    </div>
  ),
}

/** Button with icons (startDecorator, endDecorator) */
export const Icons: Story = {
  parameters: {
    docs: {
      description: {
        story: "Buttons with icon decorators at the start and end positions.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Button {...args} startDecorator={<Search size={20} />}>
        Search
      </Button>
      <Button {...args} endDecorator={<ArrowRight size={20} />}>
        Continue
      </Button>
      <Button
        {...args}
        startDecorator={<Download size={20} />}
        endDecorator={<ArrowRight size={20} />}
      >
        Download & Continue
      </Button>
      <Button {...args} variant="outline" startDecorator={<Heart size={20} />}>
        Favorite
      </Button>
      <Button {...args} variant="text" endDecorator={<InfoCircle size={20} />}>
        Learn More
      </Button>
    </div>
  ),
}

/** Button disabled states */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story: "Buttons in disabled state across all variants and colors.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Button disabled>Disabled Filled</Button>
      <Button disabled variant="outline">
        Disabled Outline
      </Button>
      <Button disabled variant="text">
        Disabled Text
      </Button>
      <Button disabled color="negative">
        Disabled Negative
      </Button>
      <Button disabled startDecorator={<Search size={16} />}>
        Disabled with Icon
      </Button>
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of button states including default, hover, focus, active, and disabled states across different variants.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "center",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Filled Buttons</Typography>
          <Button>Default</Button>
          <Button disabled>Disabled Default</Button>
          <Button color="negative">Negative</Button>
          <Button color="negative" disabled>
            Disabled Negative
          </Button>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Outline Buttons</Typography>
          <Button variant="outline">Default</Button>
          <Button variant="outline" disabled>
            Disabled Default
          </Button>
          <Button variant="outline" color="negative">
            Negative
          </Button>
          <Button variant="outline" color="negative" disabled>
            Disabled Negative
          </Button>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Text Buttons</Typography>
          <Button variant="text">Default</Button>
          <Button variant="text" disabled>
            Disabled Default
          </Button>
          <Button variant="text" color="negative">
            Negative
          </Button>
          <Button variant="text" color="negative" disabled>
            Disabled Negative
          </Button>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">With Icons</Typography>
          <Button startDecorator={<Search size={16} />}>Search</Button>
          <Button startDecorator={<Search size={16} />} disabled>Disabled Continue</Button>
          <Button variant="outline" endDecorator={<ArrowRight size={16} />}>
            Continue
          </Button>
          <Button variant="outline" endDecorator={<ArrowRight size={16} />} disabled>
            Disabled Continue
          </Button>
        </div>
      </div>
    )
  },
}


/** Button as text links using href */
export const TextLink: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Buttons rendered as anchor elements using the href prop, styled as text links.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Button href="https://example.com" variant="text">
        External Link
      </Button>
      <Button href="https://example.com" variant="text" target="_blank">
        Open in New Tab
      </Button>
      <Button
        href="https://example.com"
        variant="text"
        endDecorator={<ArrowRight size={16} />}
      >
        Learn More
      </Button>
      <Button href="https://example.com" variant="text" color="negative">
        Negative Link
      </Button>
    </div>
  ),
}

/** Button with full width */
export const FullWidth: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "Button stretches to fill the width of its container.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        gap: 16,
      }}
    >
      <Button {...args} fullWidth>
        Full Width Button
      </Button>
      <Button {...args} fullWidth variant="outline">
        Full Width Outline
      </Button>
      <Button {...args} fullWidth variant="text">
        Full Width Text
      </Button>
      <Button {...args} fullWidth startDecorator={<Download size={16} />}>
        Full Width with Icon
      </Button>
    </div>
  ),
}

/** Button with loading state */
export const LoadingState: Story = {
  parameters: {
    docs: {
      description: {
        story: "Buttons in loading state with spinning animation",
      },
    },
  },
  render: () => {
    function LoadingDemo() {
      const [loading, setLoading] = useState(false)
      const onClick = useCallback(() => {
        setLoading(true)
      }, [setLoading])

      return (
        <>
          <style>
            {`
              @keyframes spin {
                from {
                  transform: rotate(0deg);
                }
                to {
                  transform: rotate(360deg);
                }
              }
            `}
          </style>
          <Button
            id="loading-state-button"
            startDecorator={
              loading && (
                <Loading3Quarters
                  size={16}
                  style={{
                    animation: "spin 1s linear infinite",
                    transformOrigin: "center",
                  }}
                />
              )
            }
            onClick={onClick}
            disabled={loading}
          >
            {loading ? "Loading..." : "Click here for loading"}
          </Button>
        </>
      )
    }
    return <LoadingDemo />
  },
}

/** Migration guide comparing legacy vs new Button implementations */
export const Migration: Story = {
  parameters: {
    docs: {
      description: {
        story: "Visual comparison between legacy Button (@apollo/ui/legacy) and new Apollo Button (@apollo/ui) showing the migration path and key differences.",
      },
    },
  },
  render: () => {
    function MigrationDemo() {
      const [loading, setLoading] = useState(false)

      const handleLoadingDemo = () => {
        setLoading(true)
        setTimeout(() => setLoading(false), 2000)
      }

      return (
      <div style={{ display: "flex", flexDirection: "column", gap: 32 }}>
        <style>
          {`
            @keyframes spin {
              from { transform: rotate(0deg); }
              to { transform: rotate(360deg); }
            }
            .migration-section {
              padding: 20px;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              background: #f9fafb;
            }
            .migration-header {
              font-weight: 600;
              margin-bottom: 16px;
              color: #374151;
            }
            .migration-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 24px;
              margin-top: 16px;
            }
            .migration-column {
              display: flex;
              flex-direction: column;
              gap: 12px;
            }
            .migration-label {
              font-weight: 500;
              font-size: 14px;
              color: #6b7280;
              margin-bottom: 8px;
            }
            .code-block {
              background: #1f2937;
              color: #f3f4f6;
              padding: 12px;
              border-radius: 6px;
              font-family: 'Monaco', 'Menlo', monospace;
              font-size: 12px;
              line-height: 1.4;
              overflow-x: auto;
            }
            .removed { color: #fca5a5; }
            .added { color: #86efac; }
          `}
        </style>

        {/* Variant Migration */}
        <div className="migration-section">
          <div className="migration-header">🔄 Variant Migration</div>
          <div className="migration-grid">
            <div className="migration-column">
              <div className="migration-label">❌ Legacy (@apollo/ui/legacy)</div>
              <div className="code-block">
                <div className="removed">{'<Button variant="solid">Filled</Button>'}</div>
                <div className="removed">{'<Button variant="plain">Text</Button>'}</div>
                <div>{'<Button variant="outline">Outline</Button>'}</div>
              </div>
            </div>
            <div className="migration-column">
              <div className="migration-label">✅ New (@apollo/ui)</div>
              <div className="code-block">
                <div className="added">{'<Button variant="filled">Filled</Button>'}</div>
                <div className="added">{'<Button variant="text">Text</Button>'}</div>
                <div>{'<Button variant="outline">Outline</Button>'}</div>
              </div>
              <div style={{ display: "flex", gap: 8, marginTop: 8 }}>
                <Button variant="filled" size="small">Filled</Button>
                <Button variant="text" size="small">Text</Button>
                <Button variant="outline" size="small">Outline</Button>
              </div>
            </div>
          </div>
        </div>

        {/* Size Migration */}
        <div className="migration-section">
          <div className="migration-header">📏 Size Migration</div>
          <div className="migration-grid">
            <div className="migration-column">
              <div className="migration-label">❌ Legacy (3 sizes)</div>
              <div className="code-block">
                <div className="removed">{'<Button size="sm">Small</Button>'}</div>
                <div className="removed">{'<Button size="md">Medium</Button>'}</div>
                <div className="removed">{'<Button size="lg">Large</Button>'}</div>
              </div>
            </div>
            <div className="migration-column">
              <div className="migration-label">✅ New (2 sizes)</div>
              <div className="code-block">
                <div className="added">{'<Button size="small">Small</Button>'}</div>
                <div className="added">{'<Button size="large">Large</Button>'}</div>
              </div>
              <div style={{ display: "flex", gap: 8, alignItems: "center", marginTop: 8 }}>
                <Button size="small">Small</Button>
                <Button size="large">Large</Button>
              </div>
            </div>
          </div>
        </div>

        {/* Color Migration */}
        <div className="migration-section">
          <div className="migration-header">🎨 Color Migration</div>
          <div className="migration-grid">
            <div className="migration-column">
              <div className="migration-label">❌ Legacy</div>
              <div className="code-block">
                <div>{'<Button color="primary">Primary</Button>'}</div>
                <div className="removed">{'<Button color="danger">Danger</Button>'}</div>
              </div>
            </div>
            <div className="migration-column">
              <div className="migration-label">✅ New</div>
              <div className="code-block">
                <div>{'<Button color="primary">Primary</Button>'}</div>
                <div className="added">{'<Button color="negative">Negative</Button>'}</div>
              </div>
              <div style={{ display: "flex", gap: 8, marginTop: 8 }}>
                <Button color="primary" size="small">Primary</Button>
                <Button color="negative" size="small">Negative</Button>
              </div>
            </div>
          </div>
        </div>

        {/* Loading State Migration */}
        <div className="migration-section">
          <div className="migration-header">⏳ Loading State Migration</div>
          <div className="migration-grid">
            <div className="migration-column">
              <div className="migration-label">❌ Legacy (Built-in)</div>
              <div className="code-block">
                <div className="removed">{'<Button'}</div>
                <div className="removed">{'  loading={true}'}</div>
                <div className="removed">{'  loadingPosition="start"'}</div>
                <div className="removed">{'>'}</div>
                <div className="removed">{'  Saving...'}</div>
                <div className="removed">{'</Button>'}</div>
              </div>
            </div>
            <div className="migration-column">
              <div className="migration-label">✅ New (Manual Implementation)</div>
              <div className="code-block">
                <div className="added">{'<Button'}</div>
                <div className="added">{'  disabled={loading}'}</div>
                <div className="added">{'  startDecorator={'}</div>
                <div className="added">{'    loading && <LoadingIcon />'}</div>
                <div className="added">{'  }'}</div>
                <div className="added">{'>'}</div>
                <div className="added">{'  {loading ? "Saving..." : "Save"}'}</div>
                <div className="added">{'</Button>'}</div>
              </div>
              <div style={{ marginTop: 8 }}>
                <Button
                  disabled={loading}
                  startDecorator={
                    loading && (
                      <Loading3Quarters
                        size={16}
                        style={{ animation: "spin 1s linear infinite" }}
                      />
                    )
                  }
                  onClick={handleLoadingDemo}
                  size="small"
                >
                  {loading ? "Saving..." : "Try Loading Demo"}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Link Button Migration */}
        <div className="migration-section">
          <div className="migration-header">🔗 Link Button Migration</div>
          <div className="migration-grid">
            <div className="migration-column">
              <div className="migration-label">❌ Legacy (as prop)</div>
              <div className="code-block">
                <div className="removed">{'<Button'}</div>
                <div className="removed">{'  as="a"'}</div>
                <div className="removed">{'  href="/dashboard"'}</div>
                <div className="removed">{'>'}</div>
                <div className="removed">{'  Go to Dashboard'}</div>
                <div className="removed">{'</Button>'}</div>
              </div>
            </div>
            <div className="migration-column">
              <div className="migration-label">✅ New (href prop)</div>
              <div className="code-block">
                <div className="added">{'<Button'}</div>
                <div className="added">{'  href="/dashboard"'}</div>
                <div className="added">{'>'}</div>
                <div className="added">{'  Go to Dashboard'}</div>
                <div className="added">{'</Button>'}</div>
              </div>
              <div style={{ marginTop: 8 }}>
                <Button href="#" variant="text" size="small" endDecorator={<ArrowRight size={14} />}>
                  Example Link
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Summary */}
        <div className="migration-section">
          <div className="migration-header">📋 Migration Checklist</div>
          <div style={{ fontSize: 14, lineHeight: 1.6 }}>
            <div>✅ Update import: <code>@apollo/ui/legacy</code> → <code>@apollo/ui</code></div>
            <div>✅ Change variants: <code>solid</code> → <code>filled</code>, <code>plain</code> → <code>text</code></div>
            <div>✅ Update sizes: <code>sm/md/lg</code> → <code>small/large</code></div>
            <div>✅ Change colors: <code>danger</code> → <code>negative</code></div>
            <div>✅ Replace loading props with manual implementation</div>
            <div>✅ Replace <code>as</code> prop with <code>href</code> for links</div>
            <div>✅ Remove <code>action</code> prop usage</div>
          </div>
        </div>
      </div>
      )
    }
    return <MigrationDemo />
  },
}
