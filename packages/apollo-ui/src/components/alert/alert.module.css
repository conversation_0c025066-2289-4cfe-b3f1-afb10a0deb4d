@layer legacy {
    .alert {
        --apl-alert-gap: var(--apl-space-gap-xs);
        --apl-alert-padding: var(--apl-space-padding-sm);
        /* alertSuccess */
        --apl-alert-success-background-color: var(--apl-colors-surface-static-success-default);
        --apl-alert-success-border-color: var(--apl-colors-border-success-subdued);
        --apl-alert-success-color: var(--apl-colors-content-primary-default);
        /* alertInfo */
        --apl-alert-info-background-color: var(--apl-colors-surface-static-default2);
        --apl-alert-info-border-color: var(--apl-colors-border-default);
        --apl-alert-info-color: var(--apl-colors-content-description);
        /* alertWarning */
        --apl-alert-warning-background-color: var(--apl-colors-surface-static-warning-default);
        --apl-alert-warning-border-color: var(--apl-colors-border-warning-default);
        --apl-alert-warning-color: var(--apl-colors-content-warning-default);
        /* alertError */
        --apl-alert-error-background-color: var(--apl-colors-surface-static-danger-default);
        --apl-alert-error-border-color: var(--apl-colors-border-danger-subdued);
        --apl-alert-error-color: var(--apl-colors-content-danger-default);

        --apl-alert-title-font-weight: var(--apl-font-font-weight-head-line5);
        --apl-alert-content-color: var(--apl-colors-content-default);
        --apl-alert-close-button-color: var(--apl-colors-content-description);

    }


}

@layer apollo {
    .alert {
        --apl-alert-gap: var(--apl-alias-spacing-gap-gap5, 8px);
        --apl-alert-padding: var(--apl-alias-spacing-padding-padding7, 12px);
        /* alertSuccess */
        --apl-alert-success-background-color: var(--apl-alias-color-success-success-container);
        --apl-alert-success-border-color: var(--apl-alias-color-success-on-success-container);
        --apl-alert-success-color: var(--apl-alias-color-success-on-success-container);
        /* alertInfo */
        --apl-alert-info-background-color: var(--apl-alias-color-background-and-surface-surface);
        --apl-alert-info-border-color: var(--apl-alias-color-outline-and-border-outline);
        --apl-alert-info-color: var(--apl-alias-color-background-and-surface-on-surface);
        /* alertWarning */
        --apl-alert-warning-background-color: var(--apl-alias-color-warning-warning-container);
        --apl-alert-warning-border-color: var(--apl-alias-color-warning-on-warning-container);
        --apl-alert-warning-color: var(--apl-alias-color-warning-on-warning-container);
        /* alertError */
        --apl-alert-error-background-color: var(--apl-alias-color-error-error-container);
        --apl-alert-error-border-color: var(--apl-alias-color-error-on-error-container);
        --apl-alert-error-color: var(--apl-alias-color-error-on-error-container);

        --apl-alert-title-font-weight: var(--apl-alias-typography-body-large-weight-emphasized);
        --apl-alert-content-color: var(--apl-alias-color-background-and-surface-on-surface);
        --apl-alert-close-button-color: var(--apl-alias-color-background-and-surface-on-surface);
    }
}

.alertRoot {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border: 1px solid;
    gap: var(--apl-alert-gap);
    border-radius: 8px;
    padding: var(--apl-alert-padding);
    align-self: stretch;
}

.alertSuccess {
    background-color: var(--apl-alert-success-background-color);
    border-color: var(--apl-alert-success-border-color);
    color: var(--apl-alert-success-color);
}

.alertInfo {
    background-color: var(--apl-alert-info-background-color);
    border-color: var(--apl-alert-info-border-color);
    color: var(--apl-alert-info-color);
}

.alertWarning {
    background-color: var(--apl-alert-warning-background-color);
    border-color: var(--apl-alert-warning-border-color);
    color: var(--apl-alert-warning-color);
}

.alertError {
    background-color: var(--apl-alert-error-background-color);
    border-color: var(--apl-alert-error-border-color);
    color: var(--apl-alert-error-color);
}

.alertDefault {
    width: fit-content;
}

.alertFull {
    width: 100%;
}

.alertTitle {
    font-weight: var(--apl-alert-title-font-weight);
}

.alertContent {
    flex: 1;
    color: var(--apl-alert-content-color);
}

.alertContentContainer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    flex: 1 0 0;
}


.alertCloseButton {
    height: fit-content;
    color: var(--apl-alert-close-button-color);
    padding: 0;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    --apl-icon-button-size: 18px;
    --apl-icon-button-svg-size: 18px;

    &:enabled,
    &[href] {
        &:hover {
            background-color: transparent;
        }
    }

}

.alertStartDecorator {
   display: flex;
}